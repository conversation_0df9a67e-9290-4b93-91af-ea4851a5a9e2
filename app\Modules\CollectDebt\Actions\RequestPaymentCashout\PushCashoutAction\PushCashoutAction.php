<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\PushCashoutAction;

use App\Lib\NextlendCore;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;
use App\Modules\CollectDebt\Model\RequestCashoutLog;

class PushCashoutAction
{
	private const MAX_ITERATIONS = 40;
	private const EMPTY_RESULT = 'EMPTY';

	private array $processedIds = [];
	private array $excludedIds = [];
	private int $currentTimestamp;

	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->currentTimestamp = now()->timestamp;
		$this->nextlendCore = $nextlendCore;
	}


	public function run(): array
	{
		for ($i = 1; $i < self::MAX_ITERATIONS; $i++) {
			$cashout = $this->__processCreateCashout();

			if ($cashout === self::EMPTY_RESULT) {
				$this->processedIds[] = 'EMPTY';
				break;
			}

			$this->processedIds[] = $cashout->id;
		}

		return $this->processedIds;
	}

	private function __processCreateCashout()
	{
		$cashout = $this->__findCashoutRecord();

		if (!$cashout) {
			return self::EMPTY_RESULT;
		}

		$this->excludedIds[] = $cashout->id;

		// Cập nhật trạng thái đang gọi tạo yc
		$this->__updateStatusToProcessing($cashout);

		// Gọi api tạo yc cashout
		$this->__callApiCreateCashout($cashout);

		return $cashout;
	}


	private function __findCashoutRecord(): ?RequestPaymentCashout
	{
		$query = RequestPaymentCashout::query()
			->with('requestPaymentGuide')
			->where('status', CollectDebtEnum::CASHOUT_STT_NEW);

		if (!empty($this->excludedIds)) {
			$query->whereNotIn('id', $this->excludedIds);
		}

		return $query->orderByRaw(request('orderByRaw', 'id asc'))->first();
	}

	private function __updateStatusToProcessing(RequestPaymentCashout $cashout): void
	{
		$wasUpdated = RequestPaymentCashout::query()
			->where('id', $cashout->id)
			->where('status', CollectDebtEnum::CASHOUT_STT_NEW)
			->update([
				'status' => CollectDebtEnum::CASHOUT_STT_PROCESSING,
				'time_updated' => $this->currentTimestamp,
				'tries' => ++$cashout->tries
			]);

		if (!$wasUpdated) {
			throw new BusinessException('Loi khong the cap nhat trang thai la dang tao yc');
		}
	}

	private function __callApiCreateCashout(RequestPaymentCashout $cashout)
	{
		$canCreateRequest = true;

		if ($cashout->tries > 0) {
			$canCreateRequest = $this->__canCreateRequestIfNotExist($cashout);

			if (!$canCreateRequest) {
				return $cashout;
			}
		}

		$inputs = [
			'partner_request_id' => $cashout->partner_request_id,
			'type' => 6, //Loại chi tiền mặc định dịch vụ này trả tiền cho đối tác là 6
			'amount' => $cashout->amount, //Số tiền cần chuyển cho người nhận
			'contract_code' => $cashout->requestPaymentGuide->contract_code, //Mã HĐ thấu chi
			'cashout_method_code' => 'IB_OFF', //IB_ON: Chi 247, IB_OFF: Chi chậm
			'bank_code' => $cashout->requestPaymentGuide->partner_request_bank_code, //Mã Ngân hàng tưng ứng tài khoản nhận. Mã này cho hệ thống chi quy định
			'account_holder' => $cashout->requestPaymentGuide->partner_request_holdername, //Tên chủ tk nhận tiền
			'account_number' => $cashout->requestPaymentGuide->partner_request_account, //Số Tk nhận tiền
		];

		$cashoutLog = RequestCashoutLog::query()->forceCreate([
			'request_payment_guide_id' => $cashout->request_payment_guide_id,
			'request' => json_encode($inputs),
		]);

		$nextlend = $this->nextlendCore->callRequest($inputs, 'ColCashoutRequest_create', 'post');
		$decrytedResult = $this->nextlendCore->decryptData($nextlend);

		$cashoutLog->response = json_encode($decrytedResult ?? []);
		$cashoutLog->save();

		if (empty($decrytedResult['id'])) {
			throw new BusinessException('Không thể tạo yc cashout');
		}

		$wasUpdate = RequestPaymentCashout::query()->where([
			'id' => $cashout->id,
			'status' => CollectDebtEnum::CASHOUT_STT_PROCESSING
		])->update([
			'status' => CollectDebtEnum::CASHOUT_STT_CREATED_CASHOUT,
			'cashout_request_id' => $decrytedResult['id'],
			'time_created_cashout_request' => now()->timestamp,
			'time_updated' => now()->timestamp
		]);

		if (!$wasUpdate) {
			throw new BusinessException('Không thể cập nhật yc cashout');
		}

		$requestPaymentGuide = RequestPaymentGuide::query()->find($cashout->request_payment_guide_id);
		$requestPaymentGuide->status_cashout = CollectDebtEnum::RPG_STT_CASHOUT_PUSHED;
		$requestPaymentGuide->time_updated = now()->timestamp;
		$r = $requestPaymentGuide->save();

		if (!$r) {
			throw new BusinessException('Lỗi không update được về trạng thái ghi nhận đẩy yc cashout thành công');
		}

		return $cashout;
	}

	private function __canCreateRequestIfNotExist(RequestPaymentCashout $cashout): bool
	{
		$input = [
			'partner_request_id' => $cashout->partner_request_id,
			'cashout_request_id' => $cashout->cashout_request_id ?? ''
		];

		$nl = $this->nextlendCore->callRequest($input, 'ColCashoutRequest_getDetail', 'get');

		// data_not_exists
		if ($nl->isDataNotExist()) {
			return true;
		}

		$decryptData =  $this->nextlendCore->decryptData($nl);

		if (empty($cashout->cashout_request_id)) {
			$wasUpdate = RequestPaymentCashout::query()->where('id', $cashout->id)
																								 ->update([
																									'cashout_request_id' => $decryptData['id'],
																									'time_updated' => now()->timestamp,
																									'time_created_cashout_request' => $decryptData['time_created'] ?? now()->timestamp,
																									'status' => CollectDebtEnum::CASHOUT_STT_CREATED_CASHOUT
																								]);
			if (!$wasUpdate) {
				return false; // Khong can thuc hien tao yc nua
			}
		}

		return true;
	}
} // End class