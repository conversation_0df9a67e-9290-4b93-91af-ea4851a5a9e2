<?php

namespace App\Modules\CollectDebt\Controllers\RequestPaymentReceipt;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Resources\RequestPaymentReceiptResourceCollection;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Requests\RequestPayment\CreateRequestPaymentReceiptRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentReceipt\GetAllRequestPaymentReceiptRequest;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\GetAllRequestPaymentReceiptAction\GetAllRequestPaymentReceiptAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\MakeRequestPaymentFromReceiptAction\MakeRequestPaymentFromReceiptAction;

class RequestPaymentReceiptController extends Controller
{
	public function CreateRequestPaymentReceipt(CreateRequestPaymentReceiptRequest $request)
	{
		$contractCode = $request->json('data.contract_code');
		$requestPaymentGuide = RequestPaymentGuide::query()->where('contract_code', $contractCode)->first();

		if (!$requestPaymentGuide) {
			throw new BusinessException('Hệ thống chưa ghi nhận mã hợp đồng thu nợ này', 4004);
		}

		$requestPayment = RequestPayment::query()->where('partner_transaction_id', $request->json('data.partner_transaction_id'))->first();

		if ($requestPayment) {
			throw new BusinessException('Mã chứng từ đã tồn tại trong hệ thống, vui lòng sử dụng mã khác', 4004);
		}

		$partnerTransactionId = $request->json('data.partner_transaction_id');
		$partnerRequestId = '';
		
		if ($request->json('data.payment_channel_code') == 'MPOS') {
			$requestPayment = RequestPayment::query()->firstWhere([
				'contract_code' => $contractCode,
				'partner_transaction_id' => $partnerTransactionId
			]);

			if (!$requestPayment) {
				throw new BusinessException(sprintf('Mã chứng từ %s không tồn tại trong hệ thống', $partnerTransactionId), 4004);
			}

			$partnerRequestId = $requestPayment->partner_request_id;
		}

		$dto = new CreateRequestPaymentReceiptDTO(
			$contractCode,
			$request->json('data.payment_method_code'),
			$request->json('data.payment_channel_code'),
			$request->json('data.payment_account_id'),
			$partnerRequestId, // mã lệnh trích để rỗng -> sẽ update ở 1 job khác
			$partnerTransactionId,
			$request->json('data.amount'),
			now()->timestamp,
			$request->json('data.created_by'),
			$request->json('data.description', ''),
			CollectDebtEnum::RP_RECEIPT_NEW, // hoặc giá trị status phù hợp
			$requestPaymentGuide->id,
			$requestPaymentGuide->partner_code
		);

		$result = app(CreateRequestPaymentReceiptAction::class)->run($dto);

		return $this->successResponse($result);
	}

	public function MakeRequestPaymentFromReceipt()
	{
		$result = app(MakeRequestPaymentFromReceiptAction::class)->run();
		return $this->successResponse($result);
	}

	public function GetAllRequestPaymentReceipt(GetAllRequestPaymentReceiptRequest $request)
	{
		$receipts = app(GetAllRequestPaymentReceiptAction::class)->run($request);
		$resource = new RequestPaymentReceiptResourceCollection($receipts);
		$response = $resource->toArray($request);
		return $this->successResponse($response);
	}
} // End class