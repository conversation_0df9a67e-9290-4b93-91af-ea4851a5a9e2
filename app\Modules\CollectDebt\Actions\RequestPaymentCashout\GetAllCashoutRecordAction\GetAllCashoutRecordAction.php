<?php
namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\GetAllCashoutRecordAction;

use Carbon\Carbon;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;
use App\Modules\CollectDebt\Requests\RequestPaymentCashout\GetAllCashoutRecordRequest;

class GetAllCashoutRecordAction
{
	public function run(GetAllCashoutRecordRequest $request)
	{
		$cashouts = RequestPaymentCashout::query();

		$partnerCode = trim($request->json('data.partner_code', ''));
		if (!empty($request->json('data.partner_code'))) {
			$cashouts = $cashouts->whereHas('requestPaymentGuide', function ($query) use ($partnerCode) {
				return $query->where('partner_code', $partnerCode);
			});
		}

		if (!empty($request->json('data.status'))) {
			$cashouts = $cashouts->where('status', $request->json('data.status'));
		}

		if (!empty($request->json('data.partner_request_id'))) {
			$cashouts = $cashouts->where('partner_request_id', $request->json('data.partner_request_id'));
		}

		if (!empty($request->json('data.from_date'))) {
			$fromDate = Carbon::parse($request->json('data.from_date'))->startOfDay()->timestamp;
			$cashouts = $cashouts->where('time_created', '>=', $fromDate);
		}

		if (!empty($request->json('data.to_date'))) {
			$toDate = Carbon::parse($request->json('data.to_date'))->endOfDay()->timestamp;
			$cashouts = $cashouts->where('time_created', '<=', $toDate);
		}


		$cashouts = $cashouts->latest('id')->paginate(
			$request->json('data.limit', 20),
			$request->json('data.field', ['*']),
			'page',
			$request->json('data.page', 1)
		);

		return $cashouts;
	}
} // End class