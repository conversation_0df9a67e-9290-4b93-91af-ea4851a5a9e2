<?php

namespace App\Modules\CollectDebt\Enums;

class CollectDebtEnum
{
	// request payment guide
  const REQUEST_PAYMENT_GUIDE_NEW = 1;
  const REQUEST_PAYMENT_GUIDE_APPROVED = 2;
  const REQUEST_PAYMENT_GUIDE_PROCESSING_COMMAND = 3;
  const REQUEST_PAYMENT_GUIDE_DENIED = 4;
  const REQUEST_PAYMENT_GUIDE_MADE_COMMAND = 5; // da tao lenh trich
  const REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER = 6; // đã báo cho đối tác và đóng yc thu hộ
  const REQUEST_PAYMENT_GUIDE_READY_TRICH_VET = 7; // sẵn sàng để tạo lệnh trích vét

  const REQUEST_PAYMENT_AUTO = 1;
  const REQUEST_PAYMENT_MANUAL = 2;

  const MUST_SEND_TO_DEBT_PARTNER = 1;

	const RPG_NOT_READY_CLOSE = 1; // chưa sẵn sàng đóng yc
	const RPG_READY_TO_CLOSE = 2; // sẵn sàng đóng yc
	const RPG_CLOSED = 3; // đã đóng yc

	const RPG_STT_CASHOUT_UNCREATE = 1; // chưa tạo
	const RPG_STT_CASHOUT_CREATING = 2;
	const RPG_STT_CASHOUT_CREATED = 3;
	const RPG_STT_CASHOUT_PUSHED = 4;
	const RPG_STT_CASHOUT_SUCCESS_PAYOUT = 5;
	const RPG_STT_CASHOUT_FAILED_PAYOUT = 6;
	const RPG_STT_CASHOUT_EMPTY_NOTI = 7;

	// trạng thái trích nợ của lệnh trích
	const RP_STT_PAYMENT_UNSEND = 1;
	const RP_STT_PAYMENT_SENDING = 2;
	const RP_STT_PAYMENT_CANCELED = 3;
	const RP_STT_PAYMENT_SENDING_ERROR = 4;
	const RP_STT_PAYMENT_SENDED = 5;
	const RP_STT_PAYMENT_RECEIVED = 6;

	// trạng thái lệnh trích
	const RQ_STT_NEW = 1;
	const RQ_STT_APPROVED = 2;
	const RQ_STT_COMPLETED = 3;
	const RQ_STT_CANCELED = 4;
	const RQ_STT_COMPLATED_AND_RECHECK = 5;

	// request payment log
	const RQP_LOG_STT_NEW = 1;
	const RQP_LOG_STT_PROCESSING = 2;
	const RQP_LOG_STT_COMPLETED = 3;

	// receipt
	const RP_RECEIPT_NEW = 1;
	const RP_RECEIPT_PROCESSING = 2;
	const RP_RECEIPT_COMPLETED = 3;
	const RP_RECEIPT_APPROVED = 4;	
	const RP_RECEIPT_CANCELED = 5;
	const RP_RECEIPT_LATE_NOTI = 6;

	// action
	const RPA_ACTION_CANCEL = 'CANCEL';
	const RPA_STATUS_NEW = 1;
	const RPA_STATUS_PROCESSING = 2;
	const RPA_STATUS_DONE = 3;

	// cashout
	const CASHOUT_STT_NEW = 1;
	const CASHOUT_STT_PROCESSING = 2;
	const CASHOUT_STT_CREATED_CASHOUT = 3;
	const CASHOUT_STT_ERROR = 4;
	const CASHOUT_STT_FAILED = 5;
	const CASHOUT_STT_SUCCESS = 6;

	// đối tác cashout, trạng thái chi tiền cuối cùng
	const SERVICE_CASHOUT_SUCCESS = 5;
	const SERVICE_CASHOUT_FAILED = 6;
	const SERVICE_CASHOUT_REFUND = 7;

	public static function isFinalCashoutServiceState(int $statusRequest): bool {
		return in_array($statusRequest, [
			self::SERVICE_CASHOUT_SUCCESS,
			self::SERVICE_CASHOUT_FAILED,
			self::SERVICE_CASHOUT_REFUND
		]);
	}
} // End class