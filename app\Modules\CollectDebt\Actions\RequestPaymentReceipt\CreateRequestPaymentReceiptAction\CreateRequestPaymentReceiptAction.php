<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction;

use App\Modules\CollectDebt\Model\RequestPaymentReceipt;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;

class CreateRequestPaymentReceiptAction
{
	public function run(CreateRequestPaymentReceiptDTO $dto): RequestPaymentReceipt
	{
		$requestPaymentReceipt = RequestPaymentReceipt::query()->firstOrCreate([
			'contract_code' => $dto->contract_code,
			'partner_transaction_id' => $dto->partner_transaction_id,
			'partner_request_id' => $dto->partner_request_id,
		], $dto->toArray());

		return $requestPaymentReceipt;
	}
} // End class