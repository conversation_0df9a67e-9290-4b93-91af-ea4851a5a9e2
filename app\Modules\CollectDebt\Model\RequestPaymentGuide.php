<?php

namespace App\Modules\CollectDebt\Model;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Model\RequestPayment;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Modules\CollectDebt\Model\RequestPaymentGuideMerchant;

class RequestPaymentGuide extends Model
{
	protected $table = 'request_payment_guide';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

	// Relationship
	public function requestPayments()
	{
		return $this->hasMany(RequestPayment::class, 'request_payment_guide_id', 'id');
	}

	public function requestPaymentGuideMerchant(): HasOne
	{
		return $this->hasOne(RequestPaymentGuideMerchant::class, 'request_payment_guide_id', 'id');
	}
	
	// Method
	public function getMposMcId(): string
	{
		$paymentGuide = json_decode($this->payment_guide, true);
		return collect($paymentGuide)->where('payment_method_code', 'MPOS')->first()['payment_account_id'];
	}

	public function getSweepAmount(): int {
		return $this->amount - $this->amount_debited;
	}

	public function getPaymentGuideList(): array {
		return json_decode($this->payment_guide, true);
	}

	public function isExpired(): bool {
		return $this->time_expired < now()->timestamp;
	}

	public function isReadyTrichVet(): bool {
		return $this->status == CollectDebtEnum::REQUEST_PAYMENT_GUIDE_READY_TRICH_VET;
	}

	/**
	 * Không xử lý thêm bất cứ action nào với yc thu hộ nếu:
	 * 	- yc thu hộ đã được thông báo cho đối tác
	 * 	- số tiền phải thu của yc thu hộ <= 0
	 * 	- yc thu hộ đủ điều kiện báo có cho đối tác
	 * 	- yc thu hộ đã hết hạn
	 */
	public function isCanCreateSweepRequest() {
		return 	$this->getSweepAmount() > 0 
						&& !$this->isExpired() 
						&& $this->status != CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER; 
	}
} // End class
