<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\GetAllRequestPaymentGuideAction;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Controllers\Config\ConfigController;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\ExportRequestPaymentGuideRequest;

class ExportRequestPaymentGuideAction
{
	public function run(ExportRequestPaymentGuideRequest $request)
	{
		$requestPaymentGuides = RequestPaymentGuide::query();

		if (empty($request->json('data.partner_code'))) {
			throw new BusinessException('Không xác minh được thông tin đối tác');
		}

		if (!empty($request->json('data.status'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('status', $request->json('data.status'));
		}

		if (!empty($request->json('data.contract_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('contract_code', $request->json('data.contract_code'));
		}

		if (!empty($request->json('data.partner_contract_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_contract_code', $request->json('data.partner_contract_code'));
		}

		if (!empty($request->json('data.partner_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_code', $request->json('data.partner_code'));
		}

		if (!empty($request->json('data.partner_request_id'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_request_id', $request->json('data.partner_request_id'));
		}

		if (!empty($request->json('data.passport'))) {
			$requestPaymentGuides = $requestPaymentGuides->whereHas('requestPaymentGuideMerchant', function ($query) use ($request) {
				$query->where('passport', trim($request->json('data.passport')));
			});
		}

		if (!empty($request->json('data.from_date'))) {
			$fromDate = Carbon::parse($request->json('data.from_date'))->startOfDay()->timestamp;
			$requestPaymentGuides = $requestPaymentGuides->where('time_created', '>=', $fromDate);
		}

		if (!empty($request->json('data.to_date'))) {
			$toDate = Carbon::parse($request->json('data.to_date'))->endOfDay()->timestamp;
			$requestPaymentGuides = $requestPaymentGuides->where('time_created', '<=', $toDate);
		}

		if (!empty($request->json('data.type'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('type', $request->json('data.type'));
		}

		$keyword = trim($request->json('data.merchant_filter_keyword', ''));

		if (!empty($keyword)) {
			$requestPaymentGuides = $requestPaymentGuides->whereHas('requestPaymentGuideMerchant', function ($query) use ($keyword) {
				$query->where('merchant_name', 'LIKE', $keyword.'%')
				      ->orWhere('email', 'LIKE', $keyword.'%')
				      ->orWhere('mobile', 'LIKE', $keyword.'%');
			});
		}

		$limit = $request->json('data.limit', 50);
		if ($limit > 50) {
			$limit = 50;
		}

		$offset = $request->json('data.offset', 0);

		$statusTextRaw = $this->__switchingToSql();

		$requestPaymentGuides = $requestPaymentGuides->with('requestPaymentGuideMerchant:id,request_payment_guide_id,merchant_id,merchant_name,representation_name,email,mobile,address,passport')
		->latest('id')
		->limit($limit)
		->offset($offset)
		->select([
			'id',
			'contract_code',
			'amount',
			'amount_debited',
			'time_requested',
			'time_created',
			'time_expired',
			'partner_request_id',
			'status',
			DB::raw($statusTextRaw)
		])
		->get();

		return $requestPaymentGuides;
	}

	private function __switchingToSql(): string {
		$wordings = app(ConfigController::class)->GetConfigAsArray();
		$sql = " CASE status ";
		
		foreach ($wordings['request_payment_guide']['status'] as $stt) {
			$sql .= sprintf(" WHEN %s THEN '%s' ", $stt['id'], $stt['name']);
		}

		$sql .= " ELSE 'Không xác định' END AS status_text ";
		$sql = trim($sql);

		return $sql;
	}
} // End class