<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\SubAction;

use App\Lib\Helper;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\Model\RequestPaymentAction;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentVAAction;
use App\Modules\CollectDebt\Actions\RequestPayment\DetailRequestPaymentAction\DetailRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;
use Illuminate\Support\Facades\Log;

class HandleVANotiSubAction
{
	public CheckRequestPaymentVAAction $action;

	public function __construct(CheckRequestPaymentVAAction $action)
	{
		$this->action = $action;
	}

	public function run(RequestPaymentLog $requestPaymentLog, $responseJson)
	{
		$partnerTransactionId = $responseJson['data']['vaNextpayNumber'];

		$requestPayment = app(DetailRequestPaymentAction::class)->run(
			['partner_transaction_id' => $partnerTransactionId], 
			['*'], 
			[]
		);

		if (!$requestPayment) {
			return $this->markAsDone($requestPaymentLog);
		}

		// VA động
		$checkResult = $this->action->executeCheck($requestPayment);
		
		// tạo recipt
		if ($this->action->isPaidVA($checkResult)) {
			$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
				$requestPayment, 
				$requestPayment->amount_request, 
				CollectDebtEnum::RP_RECEIPT_APPROVED
			);
			
			$receipt = app(CreateRequestPaymentReceiptAction::class)->run($dto);

			if (!$receipt) {
				throw new BusinessException('Tạo nguồn tiền về VA động thất bại');
			}

			return $this->markAsDone($requestPaymentLog);
		}

		Log::info('Khong Biet La Loi Gi', ['requestPayment' => $requestPayment->toArray(), 'responseJson' => $responseJson]);
		return $this->markAsDone($requestPaymentLog);
	}

	public function createCancelDebitIfExist(RequestPayment $requestPayment)
	{
		$requestPaymentMpos = RequestPayment::query()->where([
			'request_payment_guide_id' => $requestPayment->request_payment_guide_id,
			'payment_method_code' => 'MPOS'
		])->whereNotIn('status', [
			CollectDebtEnum::RQ_STT_COMPLETED, 
			CollectDebtEnum::RQ_STT_CANCELED
		])
		->orderBy('id', 'DESC')
		->first();
		
		if (!$requestPaymentMpos) {
			return true;
		}

		// Insert vao bang action de huy
		$requestPaymentAction = RequestPaymentAction::query()->forceCreate([
			'contract_code' => $requestPayment->contract_code,
			'request_payment_id' => $requestPaymentMpos->id,
			'action' => 'CANCEL',
			'data' => '',
			'description' => 'Đặt schedule hủy lệnh trích mpos',
			'time_created' => now()->timestamp,
			'created_by' => Helper::getSystemUser()
		]);

		if (!$requestPaymentAction) {
			throw new BusinessException('Tạo action hủy lệnh trích mpos thất bại');
		}

		return $requestPaymentAction;
	}

	public function markAsDone(RequestPaymentLog $requestPaymentLog): RequestPaymentLog
	{
		$requestPaymentLog->status = CollectDebtEnum::RQP_LOG_STT_COMPLETED;
		$requestPaymentLog->updated_at = now();
		$requestPaymentLog->save();
		return $requestPaymentLog;
	}
} // End class
