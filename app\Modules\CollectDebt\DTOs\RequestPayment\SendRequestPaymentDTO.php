<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Modules\CollectDebt\Model\RequestPayment;

class SendRequestPaymentDTO
{
	public $nextlend_request_id;
	public $merchantId;
	public $lendingRequestId;
	public $debitAmount;
	public $requestTime;
	public $lendingId;
	public $loanOriginalAmount;
	public $deductionPerDayAmount;
	public $loanBalance;
	public $partner_code;
	public $partnerCode;
	public $_prefix;
	public $recoverEndTime;
	public $recoverStartTime;

	public function __construct(
		int $nextlendRequestId,
		string $merchantId,
		string $lendingRequestId,
		float $debitAmount,
		string $requestTime,
		string $lendingId,
		float $loanOriginalAmount,
		float $deductionPerDayAmount,
		float $loanBalance,
		string $partnerCode = 'NEXT_LEND',
		string $prefix = '',
		?string $recoverEndTime = null,
		?string $recoverStartTime = null
	) {
		$this->nextlend_request_id = $nextlendRequestId;
		$this->merchantId = $merchantId;
		$this->lendingRequestId = $lendingRequestId;
		$this->debitAmount = $debitAmount;
		$this->requestTime = $requestTime;
		$this->lendingId = $lendingId;
		$this->loanOriginalAmount = $loanOriginalAmount;
		$this->deductionPerDayAmount = $deductionPerDayAmount;
		$this->loanBalance = $loanBalance;
		$this->partner_code = 'MPOS';
		$this->partnerCode = $partnerCode;
		$this->_prefix = $prefix;
		$this->recoverEndTime = $recoverEndTime;
		$this->recoverStartTime = $recoverStartTime;
	}

	/**
	 * Tạo DTO từ RequestPayment model
	 */
	public static function fromRequestPayment(RequestPayment $requestPayment): self
	{
		$recoverEndTime = null;
		$recoverStartTime = null;

		if ($requestPayment->time_expired) {
			$recoverEndTime = date('YmdHis', $requestPayment->time_expired) . '999';
		}

		if ($requestPayment->time_begin) {
			$recoverStartTime = date('YmdHis', $requestPayment->time_begin) . '000';
		}

		return new self(
			$requestPayment->id,
			$requestPayment->payment_account_id,
			$requestPayment->partner_request_id,
			$requestPayment->amount_request + $requestPayment->fee,
			date('YmdHis'),
			$requestPayment->contract_code,
			$requestPayment->amount_request,
			$requestPayment->amount_request,
			$requestPayment->amount_request,
			'NEXT_LEND',
			config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE'),
			$recoverEndTime,
			$recoverStartTime
		);
	}

	/**
	 * Tạo DTO từ array
	 */
	public static function fromArray(array $data): self
	{
		return new self(
			$data['nextlend_request_id'],
			$data['merchantId'],
			$data['lendingRequestId'],
			$data['debitAmount'],
			$data['requestTime'] ?? date('YmdHis'),
			$data['lendingId'],
			$data['loanOriginalAmount'],
			$data['deductionPerDayAmount'],
			$data['loanBalance'],
			$data['partnerCode'] ?? 'NEXT_LEND',
			$data['_prefix'] ?? '',
			$data['recoverEndTime'] ?? null,
			$data['recoverStartTime'] ?? null
		);
	}

	/**
	 * Chuyển đổi DTO thành array
	 */
	public function toArray(): array
	{
		$data = get_object_vars($this);

		// Loại bỏ các giá trị null
		return array_filter($data, function ($value) {
			return $value !== null;
		});
	}
}
