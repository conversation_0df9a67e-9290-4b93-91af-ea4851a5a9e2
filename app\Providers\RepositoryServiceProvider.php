<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\CollectDebt\Interfaces\ReportRequestPaymentInterface;
use App\Modules\CollectDebt\Actions\RequestPayment\ReportRequestPaymentAction\SubAction\BidvReportRequestPayment;
use App\Modules\CollectDebt\Actions\RequestPayment\ReportRequestPaymentAction\SubAction\NextlendReportRequestPayment;

class RepositoryServiceProvider extends ServiceProvider
{
	public function register()
	{
		$this->app->bind(ReportRequestPaymentInterface::class, function ($app, $params) {
			if ($params['partner_code'] == 'NEXTLEND') {
				return $app->make(NextlendReportRequestPayment::class);
			}

			if ($params['partner_code'] == 'BIDV') {
				return $app->make(BidvReportRequestPayment::class);
			}

			return $app->make(NextlendReportRequestPayment::class);
		});
	}

	/**
	 * Bootstrap any application services.
	 *
	 * @return void
	 */
	public function boot() {}
} // End class
