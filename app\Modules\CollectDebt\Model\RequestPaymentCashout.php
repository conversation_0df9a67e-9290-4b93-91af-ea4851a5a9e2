<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;

class RequestPaymentCashout extends Model
{
	protected $table = 'request_cashout';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

	public function requestPaymentGuide()
	{
		return $this->belongsTo(RequestPaymentGuide::class, 'request_payment_guide_id', 'id');
	}
} // End class
