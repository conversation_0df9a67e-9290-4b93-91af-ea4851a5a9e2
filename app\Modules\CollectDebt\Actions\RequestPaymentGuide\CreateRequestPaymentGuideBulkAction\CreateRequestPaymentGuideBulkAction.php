<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\CreateRequestPaymentGuideBulkAction;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\CreateRequestPaymentGuideBulkRequest;

class CreateRequestPaymentGuideBulkAction
{
	public function run(CreateRequestPaymentGuideBulkRequest $request)
	{
		$partnerRequestIds = [];
		$skippedItems = [];

		foreach ($request->json('data') as $index => $item) {
			DB::beginTransaction();
			try {
				$contractCode = $item['contract_code'];

				// Kiểm tra xem contract_code đã tồn tại với status != 6 chưa
				$existingGuide = RequestPaymentGuide::query()
					->where([
						['contract_code', '=',$contractCode],
						['status', '!=', CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER]
					])
					->orWhere('partner_request_id', $item['partner_request_id'])
					->first();

				
				if ($existingGuide) {
					// Nếu đã tồn tại với status != 6, bỏ qua item này
					$skippedItems[] = $contractCode;
					DB::commit();
					continue;
				}

				$p = [
					'partner_code' => Str::of($item['partner_code'])->trim(),
					'contract_code' => Str::of($item['contract_code'])->trim(),
					'partner_request_id' => $item['partner_request_id'],
					'amount' => $item['amount'],
					'type' => $item['type'],
					'created_by' => $item['created_by'],
					'requested_by' => $item['requested_by'],
					'time_requested' => Carbon::parse($item['requested_at'])->timestamp,
					'time_expired' => Carbon::parse($item['expired_at'])->timestamp,
					'time_created' => now()->timestamp,
				];

				$requestPaymentGuide = RequestPaymentGuide::query()->firstOrCreate([
					'contract_code' => $p['contract_code'],
					'partner_request_id' => $p['partner_request_id'],
				], $p);

				if (!$requestPaymentGuide) {
					throw new BusinessException('Tạo lệnh yêu cầu thu hộ không thành công');
				}

				$partnerRequestIds[] = $requestPaymentGuide->partner_request_id;

				DB::commit();
			} catch (\Throwable $th) {
				DB::rollBack();
				throw new BusinessException($th->getMessage());
			}
		}

		return [
			'partner_request_id' => $partnerRequestIds,
			'skipped_items' => $skippedItems,
			'total_processed' => count($partnerRequestIds),
			'total_skipped' => count($skippedItems)
		];
	}
} // End class
