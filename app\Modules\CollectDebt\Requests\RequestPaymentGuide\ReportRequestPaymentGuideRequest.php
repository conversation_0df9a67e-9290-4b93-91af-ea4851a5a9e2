<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;

class ReportRequestPaymentGuideRequest extends FormRequest
{
  public function authorize()
  {
    return true;
  }

  public function rules()
  {
    return [
      'data' => ['bail', 'required', 'array'],
      'data.partner_code' => ['required', 'string', 'max:15'],
      'data.list_contract_code' => ['required', 'array'],
      'data.list_contract_code.*' => ['required', 'string', 'max:50'],
    ];
  }

  public function messages()
  {
    return [];
  }
} // End class
