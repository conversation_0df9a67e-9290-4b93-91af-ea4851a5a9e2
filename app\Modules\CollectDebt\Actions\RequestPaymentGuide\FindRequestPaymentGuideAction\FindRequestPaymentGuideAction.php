<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\FindRequestPaymentGuideAction;

use App\Lib\Helper;
use App\Lib\NextlendCore;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use Illuminate\Support\Arr;

class FindRequestPaymentGuideAction
{
	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}

	public function callGetContractDataCore(string $contractCode) {
		$contract = $this->nextlendCore->callRequest([
			'contract_code' => $contractCode,
		], 'ThuhoSupportApps_getDetailContractAndDocumentByContractCode', 'get');

		$decryptData = $contract->decryptData();
		return $decryptData;
	}

	public function getUploadInfoFromContractResult(array $contractData) {
		return $contractData['upload_info'] ?? [];
	}

	public function getContractData(array $decryptData=[]) {
		$groupData = [
			'THONG_TIN_CHU_HO' => [],
			'THONG_TIN_HO_KINH_DOANH' => [],
			'THONG_TIN_NGUOI_THAM_CHIEU' => [],
			'THU_NHAP_TU_HO_KINH_DOANH' => [],
			'THU_NHAP_KHAC' => [],
			'TAI_SAN_KHAC' => [],
			'THONG_TIN_VAY' => []
		];


		if (empty($decryptData['contract'])) {
			throw new BusinessException('Không tìm thấy hợp đồng');
		}
		
		$groupData['THONG_TIN_VAY'][] = [
			'label' => 'Số tiền vay (vnđ)',
			'value' => Helper::numberFormat(data_get($decryptData, 'contract.amount', 0))
		];

		
		$groupData['THONG_TIN_VAY'][] = [
			'label' => 'Phí (vnđ)',
			'value' => Helper::numberFormat(data_get($decryptData, 'contract.amount_fee_evaluation', 0))
		];

		$groupData['THONG_TIN_VAY'][] = [
			'label' => 'Trạng thái',
			'value' => $decryptData['contract_partner']['status_action']
		];
		
		$otherDataPartner = data_get($decryptData, 'document_data.other.bidv_other_data.text');
		
		
		$otherDataPartnerAsArray = [];
		$otherDataPartnerAsArray = json_decode($otherDataPartner, true);
		if (!empty($otherDataPartnerAsArray)) {
			foreach ($otherDataPartnerAsArray as $groupInfo) {
				if (empty($groupInfo['code']) || empty($groupInfo['value'])) {
					continue;
				}
				
				
				switch ($groupInfo['code']) {
					case 'THONG_TIN_CHU_HO':
						$groupData['THONG_TIN_CHU_HO']['name'] = 'Thông tin chủ hộ kinh doanh';
						break;

					case 'THONG_TIN_HO_KINH_DOANH':
						$groupData['THONG_TIN_HO_KINH_DOANH']['name'] = 'Thông tin kinh doanh';
						break;

					case 'THONG_TIN_NGUOI_THAM_CHIEU':
						$groupData['THONG_TIN_NGUOI_THAM_CHIEU']['name'] = 'Thông tin tham chiếu';
						break;

					case 'THU_NHAP_TU_HO_KINH_DOANH':
						$groupData['THU_NHAP_TU_HO_KINH_DOANH']['name'] = 'Thông tin khả năng tài chính';
					break;

					case 'THU_NHAP_KHAC':
						$groupData['THU_NHAP_KHAC']['name'] = 'Thu nhập khác';
					break;

					case 'TAI_SAN_KHAC':
						$groupData['THU_NHAP_KHAC']['name'] = 'Tài sản đang sở hữu';
					break;
				}

				

				foreach ($groupInfo['value'] as $profileLabel => $profileValue) {
					switch ($profileLabel) {
					/**------------- Thong tin kinh doanh ---------- */
						case 'name':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Tên chủ hộ kinh doanh',
								'value' => $profileValue
							];
							break;

						case 'birth_day':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Ngày sinh',
								'value' => $profileValue
							];
							break;

						case 'gender':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Giới tính',
								'value' => $profileValue
							];
							break;

						case 'id_number':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Số CCCD/Hộ chiếu',
								'value' => $profileValue
							];
							break;

						case 'issue_date':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Ngày cấp',
								'value' => $profileValue
							];
							break;

						case 'issue_place':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Nơi cấp',
								'value' => $profileValue
							];
							break;

						case 'permanent_address':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Địa chỉ thường trú',
								'value' => $profileValue
							];
							break;
							
						case 'current_address':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Địa chỉ hiện tại',
								'value' => $profileValue
							];
							break;

						case 'landline':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Điện thoại bàn',
								'value' => $profileValue
							];
							break;

						case 'mobile':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Di động',
								'value' => $profileValue
							];
							break;

						case 'email':
							$groupData['THONG_TIN_CHU_HO'][]=  [
								'label' => 'Email',
								'value' => $profileValue
							];
							break;

					/**------------- Thong tin kinh doanh ---------- */
						case 'business_name':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Tên hộ kinh doanh',
								'value' => $profileValue
							];
							break;
						case 'business_address':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Địa chỉ kinh doanh',
								'value' => $profileValue
							];
							break;
						case 'business_phone':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Số điện thoại',
								'value' => $profileValue
							];
							break;
						case 'business_license':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Giấy phép kinh doanh',
								'value' => $profileValue
							];
							break;
						case 'business_issue_date':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Ngày cấp',
								'value' => $profileValue
							];
							break;
						case 'business_issue_place':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Nơi cấp',
								'value' => $profileValue
							];
							break;
						case 'business_field':
							$groupData['THONG_TIN_HO_KINH_DOANH'][]=  [
								'label' => 'Ngành nghề',
								'value' => $profileValue
							];
							break;
					
					/**------------- Thong tin tham chieu ---------- */
						case 'reference_name':
							$groupData['THONG_TIN_NGUOI_THAM_CHIEU'][]=  [
								'label' => 'Họ tên người tham chiếu',
								'value' => $profileValue
							];
							break;

						case 'reference_relation':
							$groupData['THONG_TIN_NGUOI_THAM_CHIEU'][]=  [
								'label' => 'Quan hệ với bên vay',
								'value' => $profileValue
							];
							break;
						case 'reference_phone':
							$groupData['THONG_TIN_NGUOI_THAM_CHIEU'][]=  [
								'label' => 'Số điện thoại tham chiếu',
								'value' => $profileValue
							];
							break;
						case 'reference_gender':
							$groupData['THONG_TIN_NGUOI_THAM_CHIEU'][]=  [
								'label' => 'Giới tính',
								'value' => $profileValue
							];
							break;
						case 'reference_address':
							$groupData['THONG_TIN_NGUOI_THAM_CHIEU'][]=  [
								'label' => 'Địa chỉ',
								'value' => $profileValue
							];
							break;
					/**------------- Thong tin kha nang tai chinh ---------- */
						case 'revenue':
							$groupData['THU_NHAP_TU_HO_KINH_DOANH'][]=  [ 'label' => 'Doanh thu hộ kinh doanh', 'value' => $profileValue ]; 
							break;

						case 'expense':
							$groupData['THU_NHAP_TU_HO_KINH_DOANH'][]=  [ 'label' => 'Chi phí hợp lý', 'value' => $profileValue ]; 
							break;

						case 'profit':
							$groupData['THU_NHAP_TU_HO_KINH_DOANH'][]=  [ 'label' => 'Lợi nhuận', 'value' => $profileValue ]; 
							break;
					
					/**------------- Thu nhap khac ---------- */
						case 'salary':
							$groupData['THU_NHAP_KHAC'][]=  [ 'label' => 'Lương', 'value' => $profileValue ]; 
							break;

						case 'rental':
							$groupData['THU_NHAP_KHAC'][]=  [ 'label' => 'Cho thuê nhà', 'value' => $profileValue ]; 
							break;

						case 'other':
							if ($groupInfo['code'] == 'other_income') {
								$groupData['THU_NHAP_KHAC'][]=  [ 'label' => 'Khác', 'value' => $profileValue ]; 
							}
							
							break;
					}
				}

				
				
			} // End foreach

			$taiSanKhac = collect($otherDataPartnerAsArray)->where('code', 'owned_assets')->first();
			$taiSanKhacValueFlatten = collect($taiSanKhac['value'])->flatten(1)->all();

			foreach ($taiSanKhacValueFlatten as $item) {
				$groupData['TAI_SAN_KHAC'][] =  [ 'label' => $item['name'], 'value' => $item['value'] ]; 
			}
		}

		return $groupData;
	}

	public function run(Request $request)
	{
		$requestPaymentGuide = $this->__findRecord($request);

		$contractData = $this->callGetContractDataCore($requestPaymentGuide->contract_code);

		$groupData = $this->getContractData($contractData);

		$requestPaymentGuide->requestPaymentGuideMerchant->other_data = $groupData;
		$requestPaymentGuide->requestPaymentGuideMerchant->upload_info = $this->getUploadInfoFromContractResult($contractData);

		return $requestPaymentGuide;
	}

	private function __findRecord(Request $request): RequestPaymentGuide
	{
		$where = [
			'id' => $request->json('data.request_payment_guide_id'),
			'partner_code' => $request->json('data.partner_code'),
		];

		$requestPaymentGuide = RequestPaymentGuide::query()->with('requestPaymentGuideMerchant')->firstWhere($where);

		if (!$requestPaymentGuide) {
			throw new BusinessException('Không tìm thấy thông tin yêu cầu thu hộ');
		}

		return $requestPaymentGuide;
	}

	private function __wordingContractStatus(int $status): string {
		$contractStatus = [
			8 => '<span class="badge badge-secondary">Đang thu thập HS</span>',
			1 => '<span class="badge badge-warning">Chờ thẩm định 1</span>',
			7 => '<span class="badge badge-warning">Chờ thẩm định 2</span>',
			2 => '<span class="badge badge-warning">Đã duyệt</span>',
			3 => '<span class="badge badge-danger">Từ chối</span>',
			4 => '<span class="badge badge-primary">Đã tạo yêu cầu giải ngân</span>',
			5 => '<span class="badge badge-info">Đã duyệt yêu cầu giải ngân</span>',
			6 => '<span class="badge badge-success">Đã giải ngân</span>',
			9 => '<span class="badge badge-dark">Hồ sơ sai thông tin</span>',
		];

		return $contractStatus[$status] ?? 'Chưa rõ thông tin';
	}

	private function __wordingContractStatusStep(int $status): string {
		$contractStatus = [
			8 => 'STEP_8',
			1 => 'STEP_1',
			7 => 'STEP_7',
			2 => 'STEP_2',
			3 => 'STEP_3',
			4 => 'STEP_4',
			5 => 'STEP_5',
			6 => 'STEP_6',
			9 => 'STEP_9',
		];

		return $contractStatus[$status] ?? 'Chưa rõ thông tin';
	}
} // End class