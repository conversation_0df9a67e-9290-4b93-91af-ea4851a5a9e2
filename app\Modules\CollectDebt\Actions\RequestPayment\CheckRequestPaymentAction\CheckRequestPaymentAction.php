<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\DTOs\RequestPayment\CheckRequestPaymentDTO;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CheckRequestPaymentAction
{
	private array $__processedIds = [];

	private array $__excludedIds = [];

	public CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}


	public function run()
	{
		try {
			for ($i = 1; $i < 40; $i++) {

				$requestPayment = $this->processCheckRequestPayment();
				if ($requestPayment == 'EMPTY') {
					break;
				}

				$this->__processedIds[] = $requestPayment->id;
			}
		} catch (\Throwable $th) {
			throw $th;
		}

		return $this->__processedIds;
	}

	public function processCheckRequestPayment()
	{
		$requestPayment = RequestPayment::query()
			->whereIn('status_payment', [
				CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR,
				CollectDebtEnum::RP_STT_PAYMENT_SENDED,
			])
			->whereRaw('time_checked < ' . now()->timestamp);

		if (!empty($this->__excludedIds)) {
			$requestPayment = $requestPayment->whereNotIn('id', $this->__excludedIds);
		}

		$requestPayment = $requestPayment->orderBy('time_checked', 'asc')->first();

		if (!$requestPayment) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $requestPayment->id;

		$checkResult = $this->executeCheck($requestPayment);
		$this->handleCheckResult($requestPayment, $checkResult);

		return $requestPayment;
	}

	public function executeCheck(RequestPayment $requestPayment)
	{
		$dto = CheckRequestPaymentDTO::fromRequestPayment($requestPayment);
		return $this->mposCollectDebtGatewayRepo->checkCollectDebtCommandOnPartner($dto->toArray());
	}

	public function handleCheckResult(RequestPayment $requestPayment, array $checkResult): string
	{
		$updatedData = [
			'time_checked' => now()->addMinutes(30)->timestamp,
			'checked_by' => Helper::getSystemUser(),
		];

		if (empty($requestPayment->partner_transaction_id) && !empty($checkResult['data']['data']['mposDebtId'])) {
			$updatedData['partner_transaction_id'] = $checkResult['data']['data']['mposDebtId'];
		}

		if (empty($requestPayment->time_sended)) {
			$updatedData['time_sended'] = now()->timestamp;
		}

		$requestPaymentStatus = $checkResult['data']['data']['debtStatus'] ?? '';
		switch ($requestPaymentStatus) {
			case 'PENDING':
				$updatedData = array_merge($updatedData, [
					'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDED,
					'sended_by' => Helper::getSystemUser(),
				]);

				$requestPayment->update($updatedData);
				break;

			case 'APPROVED':
				$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment(
					$requestPayment, 
					$checkResult['data']['data']['debtRecoveryAmount'],
					CollectDebtEnum::RP_RECEIPT_APPROVED
				);
				app(CreateRequestPaymentReceiptAction::class)->run($dto);
				break;

			case 'CANCEL':
			case 'EXPIRED':
				$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
				app(CreateRequestPaymentReceiptAction::class)->run($dto);
				break;

			case 'TIMEOUT':
				// chua xu ly
				break;


			default:
				// chua xu ly
				break;
		}

		return $requestPaymentStatus;
	}

	public function getDebitCmdStatus(array $checkResult): string {
		if (data_get($checkResult, 'data.error.code') == -46005) {
			return 'NOTFOUND';
		}

		$requestPaymentStatus = $checkResult['data']['data']['debtStatus'] ?? '';
		return $requestPaymentStatus;
	}

	public function getAmountApproved($checkResult=[]) {
		$amountApproved = $checkResult['data']['data']['debtRecoveryAmount'] ?? 0;
		return $amountApproved;
	}
} // End class
