<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\NotifyRequestPaymentAction;

use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPaymentLog;

class NotifyRequestPaymentAction
{
	public function run(Request $request): RequestPaymentLog
	{
		$input = $request->all();

		$requestPaymentLog = RequestPaymentLog::query()->forceCreate([
			'contract_code' => '',
			'func' => 'notify',
			'response' => json_encode($input)
		]);

		if (!$requestPaymentLog) {
			throw new BusinessException('Không thể nhận được thông tin noti', 5003);
		}

		return $requestPaymentLog;
	}
} // End class
