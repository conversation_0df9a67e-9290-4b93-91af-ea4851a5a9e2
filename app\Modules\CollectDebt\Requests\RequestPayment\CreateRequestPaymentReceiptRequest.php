<?php

namespace App\Modules\CollectDebt\Requests\RequestPayment;

use Illuminate\Foundation\Http\FormRequest;

class CreateRequestPaymentReceiptRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.contract_code' => ['required', 'string', 'max:50'],
			'data.payment_method_code' => ['required', 'string', 'in:MPOS,IB_OFF,VIRTUALACCOUNT'],
			'data.payment_channel_code' => ['required', 'string', 'max:25'],
			'data.payment_account_id' => ['required', 'string', 'max:50', 'regex:/^[A-Za-z0-9_-]+$/'],
			'data.partner_transaction_id' => ['required', 'string', 'max:50',], // mã chứng từ
			'data.amount' => ['required', 'numeric', 'integer', 'min:0'],
			'data.created_by' => ['required', 'string', 'max:255'],
			'data.description' => ['nullable', 'string', 'max:255'],
		];
	}

	public function messages()
	{
		return [
			'data.required' => 'Dữ liệu không được để trống',
			'data.array' => 'Dữ liệu phải là một mảng',
			'data.contract_code.required' => 'Mã hợp đồng không được để trống',
			'data.contract_code.string' => 'Mã hợp đồng phải là chuỗi ký tự',
			'data.contract_code.max' => 'Mã hợp đồng không được vượt quá 50 ký tự',
			'data.payment_method_code.required' => 'Phương thức thanh toán không được để trống',
			'data.payment_method_code.string' => 'Phương thức thanh toán phải là chuỗi ký tự',
			'data.payment_method_code.in' => 'Phương thức thu nợ phải là một trong: MPOS, IB_OFF, PAYON',
			'data.payment_channel_code.required' => 'Kênh thanh toán không được để trống',
			'data.payment_channel_code.string' => 'Kênh thanh toán phải là chuỗi ký tự',
			'data.payment_channel_code.max' => 'Kênh thanh toán không được vượt quá 25 ký tự',
			'data.payment_account_id.required' => 'ID tài khoản thanh toán không được để trống',
			'data.payment_account_id.string' => 'ID tài khoản thanh toán phải là chuỗi ký tự',
			'data.payment_account_id.max' => 'ID tài khoản thanh toán không được vượt quá 50 ký tự',
			'data.payment_account_id.regex' => 'ID tài khoản thanh toán chỉ được chứa chữ cái, chữ số, dấu gạch ngang (-) và dấu gạch dưới (_)',
			'data.partner_transaction_id.required' => 'Mã chứng từ không được để trống',
			'data.partner_transaction_id.string' => 'Mã chứng từ phải là chuỗi ký tự',
			'data.partner_transaction_id.max' => 'Mã chứng từ không được vượt quá 50 ký tự',
			'data.amount.required' => 'Số tiền không được để trống',
			'data.amount.numeric' => 'Số tiền phải là số',
			'data.amount.integer' => 'Số tiền phải là số nguyên',
			'data.amount.min' => 'Số tiền phải lớn hơn hoặc bằng 0',
			'data.created_by.required' => 'Người tạo không được để trống',
			'data.created_by.string' => 'Người tạo phải là chuỗi ký tự',
			'data.created_by.max' => 'Người tạo không được vượt quá 255 ký tự',
			'data.description.string' => 'Mô tả phải là chuỗi ký tự',
			'data.description.max' => 'Mô tả không được vượt quá 255 ký tự',
		];
	}

	/**
	 * Configure the validator instance.
	 */
	public function withValidator($validator)
	{
		$validator->after(function ($validator) {
			$data = $this->input('data');
			
			if (!$data) {
				return;
			}

			$paymentMethodCode = $data['payment_method_code'] ?? '';
			$paymentChannelCode = $data['payment_channel_code'] ?? '';
			$paymentAccountId = $data['payment_account_id'] ?? '';
			$contractCode = $data['contract_code'] ?? '';

			// Rule 2: MPOS validation
			if ($paymentMethodCode === 'MPOS') {
				if ($paymentChannelCode !== 'MPOS') {
					$validator->errors()->add('data.payment_channel_code', 'Khi payment_method_code=MPOS thì payment_channel_code phải bằng MPOS');
				}
				if (empty($paymentAccountId)) {
					$validator->errors()->add('data.payment_account_id', 'Khi payment_method_code=MPOS thì payment_account_id phải là mã merchant');
				}
			}

			// Rule 3: IB_OFF validation
			if ($paymentMethodCode === 'IB_OFF') {
				if ($paymentChannelCode !== 'IB_OFF') {
					$validator->errors()->add('data.payment_channel_code', 'Khi payment_method_code=IB_OFF thì payment_channel_code phải bằng IB_OFF');
				}
				if ($paymentAccountId !== $contractCode) {
					$validator->errors()->add('data.payment_account_id', 'Khi payment_method_code=IB_OFF thì payment_account_id phải bằng contract_code');
				}
			}

			// Rule 4: PAYON validation
			if ($paymentMethodCode === 'VIRTUALACCOUNT') {
				if (empty($paymentAccountId)) {
					$validator->errors()->add('data.payment_account_id', 'Khi payment_method_code=PAYON thì payment_account_id phải là mã giao dịch VA');
				}
			}
		});
	}
} // End class
