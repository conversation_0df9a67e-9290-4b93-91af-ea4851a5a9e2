<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class XuLyLenhTrichThanhCongSubAction
{
	/**
	 * {"errorCode":"00","data":{"data":{"lendingRequestId":"NEXTLENDV4-NLTC12345678901","mposDebtId":141390793},"status":true,"error":{"code":1000,"message":"DO_SERVICE_SUCCESS","messageEn":""}}} 
	 */
  public function run(RequestPayment $requestPayment, $sendDebtResult): RequestPayment
  {
		$requestPayment->update([
			'time_sended' => now()->timestamp,
			'partner_transaction_id' => $sendDebtResult['data']['data']['mposDebtId'],
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDED,
			'status' => CollectDebtEnum::RQ_STT_APPROVED,
			'sended_by' => Helper::getSystemUser(),
		]);
    
    return $requestPayment;
  }
} // End class