<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LoggingMiddleware
{
    public $timeStart;

    protected $excepts = [
      'healthcheck/liveness',
      'healthcheck/readiness',
			'GetListNotification'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
      app()->setLocale($request->json('lang', 'vi'));
      
      // DB::enableQueryLog();
      app('mylog')->concatData([
        'path' => env('APP_URL') . '/' . ltrim(request()->path(), '/'),
        'request' => $request->all(),
      ]);
      return $next($request);
    }

    public function terminate($request, $response)
    {
      app('mylog')->concatData(['response' => $response]);
      // app('mylog')->concatData(['query' => DB::getQueryLog()]);
      app('mylog')->concatData([
				'timing_second' => microtime(true) - LARAVEL_START,
				'max_execution_time' => @ini_get('max_execution_time')
			]);

      if ( $this->isNeedWriteLogForRouting($request) ) {
        app('mylog')->logging();
      }
    }

    public function isNeedWriteLogForRouting($request): bool {
      foreach ($this->excepts as $exceptPath) {
        $path = $request->fullUrl();
        if ( Str::contains($path, $exceptPath) ) {
          return false;
        }
      }

      return true;
    }
}
