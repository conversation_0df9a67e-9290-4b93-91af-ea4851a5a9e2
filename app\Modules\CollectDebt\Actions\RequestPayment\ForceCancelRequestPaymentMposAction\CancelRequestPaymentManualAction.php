<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\ForceCancelRequestPaymentMposAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Requests\RequestPayment\CancelRequestPaymentManualRequest;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction\CancelRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CancelRequestPaymentManualAction
{
	public function run(CancelRequestPaymentManualRequest $request)
	{
		$requestPayment = RequestPayment::query()->with('requestPaymentGuide:id,partner_code')->where([
			'partner_request_id' => $request->json('data.partner_request_id'),
		])
			->first();

		if (!$requestPayment) {
			throw new BusinessException('Lệnh trích không tồn tại', 4004);
		}

		if ($requestPayment->payment_method_code != 'MPOS') {
			throw new BusinessException('Không thể hủy lệnh trích đi qua kênh ' . $requestPayment->payment_method_code, 4004);
		}

		if ($requestPayment->requestPaymentGuide->partner_code !== $request->json('data.partner_code')) {
			throw new BusinessException('Lệnh trích không đúng với partner. Từ chối yêu cầu hủy', 4004);
		}

		if ($requestPayment->requestPaymentGuide->partner_code !== $request->json('data.partner_code')) {
			throw new BusinessException('Lệnh trích nợ không đi qua kênh MPOS. Không thể hủy!!', 4004);
		}

		if ($requestPayment->isFinalStatusPayment() || $requestPayment->isFinalStatus()) {
			throw new BusinessException('Lệnh trích nợ đã về trạng thái cuối. Không thể hủy!!', 4004);
		}


		$checkResult = app(CheckRequestPaymentAction::class)->executeCheck($requestPayment);
		$debitCmdStatus = app(CheckRequestPaymentAction::class)->getDebitCmdStatus($checkResult);

		if ($debitCmdStatus == 'APPROVED') {
			throw new BusinessException('Lệnh trích nợ đã thu được tiền. Không thể hủy!!', 4004);
		}

		if ($debitCmdStatus == 'CANCEL') {
			$this->createReceipt($requestPayment, $request->json('data.username'));
			return $requestPayment;
		}

		if ($debitCmdStatus == 'EXPIRED') {
			$this->createReceipt($requestPayment, $request->json('data.username'));
			return $requestPayment;
		}

		if ($debitCmdStatus == 'NOTFOUND') {
			$this->createReceipt($requestPayment, $request->json('data.username'));
			return $requestPayment;
		}

		$cancelRequest = app(CancelRequestPaymentAction::class)->run($requestPayment);
		$isCancelSuccess = CancelRequestPaymentAction::isCancelSuccess($cancelRequest);

		if (!$isCancelSuccess) {
			throw new BusinessException('Lệnh trích nợ không thể hủy. Vui lòng thử lại sau!!', 4004);
		}

		$this->createReceipt($requestPayment, $request->json('data.username'));

		return $requestPayment;
	}

	public function createReceipt(RequestPayment $requestPayment, string $username): void
	{
		$wasUpdateCancel = $requestPayment->update([
			'status' => CollectDebtEnum::RQ_STT_CANCELED,
			'time_canceled' => now()->timestamp,
			'time_canceled_payment' => now()->timestamp,
			'canceled_by' =>  $username,
			'canceled_payment_by' => $username,
		]);

		if (!$wasUpdateCancel) {
			throw new BusinessException('Lỗi không cập nhật được lệnh trích về thành đã hủy');
		}

		$dto = CreateRequestPaymentReceiptDTO::fromRequestPayment($requestPayment, 0, CollectDebtEnum::RP_RECEIPT_APPROVED);
		app(CreateRequestPaymentReceiptAction::class)->run($dto);
		return;
	}
} // End class
