<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;

class CreateRequestPaymentGuideBulkRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules() 
	{
		return [
			'data' => ['bail', 'required', 'array'],
			'data.*.partner_code' => ['bail', 'required', 'string', 'max:15'],
			'data.*.contract_code' => ['bail', 'required', 'string', 'max:50'],
			'data.*.amount' => ['bail', 'required', 'numeric', 'integer', 'min:1000', 'max:100000000000'],
			'data.*.partner_request_id' => ['bail', 'required', 'string', 'max:50', 'regex:/^[A-Z0-9]+$/'], // mã đối tác
			'data.*.type' => ['bail', 'required', 'in:1,2'],

			'data.*.created_by' => ['bail', 'required', 'string', 'max:255'],

			'data.*.expired_at' => ['bail', 'required', 'date_format:Y-m-d H:i:s', 'after:+8 hours'],

			'data.*.requested_at' => ['bail', 'required', 'date_format:Y-m-d H:i:s'],
			'data.*.requested_by' => ['bail', 'required', 'string', 'max:255'],
		];
	}

	public function messages()
	{
		return [
			'data.*.partner_code.required' => 'Mã đối tác không được để trống',
			'data.*.partner_code.string' => 'Mã đối tác phải là chuỗi',
			'data.*.partner_code.max' => 'Mã đối tác không được vượt quá 15 ký tự',

			'data.*.contract_code.required' => 'Mã hợp đồng không được để trống',
			'data.*.contract_code.string' => 'Mã hợp đồng phải là chuỗi',
			'data.*.contract_code.max' => 'Mã hợp đồng không được vượt quá 50 ký tự',

			'data.*.amount.required' => 'Số tiền không được để trống',
			'data.*.amount.numeric' => 'Số tiền phải là số',
			'data.*.amount.integer' => 'Số tiền phải là số nguyên',
			'data.*.amount.min' => 'Số tiền phải lớn hơn 1000',
			'data.*.amount.max' => 'Số tiền không được vượt quá 100 tỷ',

			'data.*.partner_request_id.required' => 'Mã yêu cầu thu hộ không được để trống',
			'data.*.partner_request_id.string' => 'Mã yêu cầu thu hộ phải là chuỗi',
			'data.*.partner_request_id.max' => 'Mã yêu cầu thu hộ không được vượt quá 50 ký tự',
			'data.*.partner_request_id.regex' => 'Mã yêu cầu thu hộ phải là chuỗi gồm các ký tự A-Z và 0-9',

			'data.*.created_by.required' => 'Người tạo không được để trống',
			'data.*.created_by.string' => 'Người tạo phải là chuỗi',
			'data.*.created_by.max' => 'Người tạo không được vượt quá 255 ký tự',

			'data.*.type.in' => 'Loại hợp đồng phải thuộc giá trị: 1-Thấu chi; 2-Khoản ứng',

			'data.*.expired_at.required' => 'Ngày hết hạn không được để trống',
			'data.*.expired_at.date_format' => 'Ngày hết hạn phải đúng định dạng Y-m-d H:i:s',
			'data.*.expired_at.after' => 'Ngày hết hạn phải lớn hơn 8 giờ',

			'data.*.requested_at.required' => 'Ngày yêu cầu không được để trống',
			'data.*.requested_at.date_format' => 'Ngày yêu cầu phải đúng định dạng Y-m-d H:i:s',

			'data.*.requested_by.required' => 'Người yêu cầu không được để trống',
			'data.*.requested_by.string' => 'Người yêu cầu phải là chuỗi',
			'data.*.requested_by.max' => 'Người yêu cầu không được vượt quá 255 ký tự',

			'data.*.created_by.required' => 'Người tạo không được để trống',
			'data.*.created_by.string' => 'Người tạo phải là chuỗi',
			'data.*.created_by.max' => 'Người tạo không được vượt quá 255 ký tự',
		];
	}

	/**
	 * Configure the validator instance.
	 */
	public function withValidator($validator)
	{
		$validator->after(function ($validator) {
			$data = $this->input('data');

			if (!$data || !is_array($data)) {
				return;
			}

			// Kiểm tra contract_code phải khác nhau
			$contractCodes = [];
			$duplicates = [];

			foreach ($data as $index => $item) {
				$contractCode = $item['contract_code'] ?? '';

				if (empty($contractCode)) {
					continue;
				}

				if (in_array($contractCode, $contractCodes)) {
					$duplicates[] = $contractCode;
				} else {
					$contractCodes[] = $contractCode;
				}
			}

			// Nếu có contract_code trùng lặp
			if (!empty($duplicates)) {
				$uniqueDuplicates = array_unique($duplicates);
				$duplicateList = implode(', ', $uniqueDuplicates);
				$validator->errors()->add('data', "Các contract_code phải khác nhau. Các mã trùng lặp: {$duplicateList}");
			}
		});
	}
} // End class
