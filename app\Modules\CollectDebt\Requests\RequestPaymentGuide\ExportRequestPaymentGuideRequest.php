<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;

class ExportRequestPaymentGuideRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string'],
			'data.contract_code' => ['nullable', 'string'],
			'data.from_date' => ['nullable', 'string', 'date_format:Y-m-d'],
			'data.to_date' => ['nullable', 'string', 'date_format:Y-m-d', 'after_or_equal:data.from_date'],
		];
	}

	public function messages()
	{
		return [];
	}

} // End class
