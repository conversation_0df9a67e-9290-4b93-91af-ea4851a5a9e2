# API FindRequestPaymentGuide

## Tổng quan
API này dùng để tra cứu chi tiết một chỉ dẫn thanh toán (Request Payment Guide) theo ID và mã đối tác.

## Thông tin cơ bản
- **URL**: `/FindRequestPaymentGuide`
- **Method**: `GET`
- **Content-Type**: `application/json`

## Ví dụ sử dụng với cURL
```bash
curl --location --request GET 'http://nextpay-web.local.com/request-debt-bidv/public/FindRequestPaymentGuide' \
--header 'Content-Type: application/json' \
--data '{
  "data": {
    "request_payment_guide_id": 11,
    "partner_code": "NEXTLEND"
  },
  "checksum": "dfgdg456456456",
  "channel_code": "WEBPARTNER",
  "time_request": **********,
  "version": "1.0"
}'
```

## Request

### Headers
```
Content-Type: application/json
```

### Body Parameters
| Tham số | <PERSON><PERSON><PERSON> dữ liệu | B<PERSON>t buộc | <PERSON>ô tả |
|---------|--------------|----------|-------|
| `data.request_payment_guide_id` | integer | ✅ | ID chỉ dẫn thanh toán |
| `data.partner_code` | string | ✅ | Mã đối tác (VD: NEXTLEND, TNEX) |
| `checksum` | string | ✅ | Checksum xác thực request |
| `channel_code` | string | ✅ | Kênh gửi request (VD: WEBPARTNER) |
| `time_request` | integer | ✅ | Thời gian gửi request (timestamp) |
| `version` | string | ✅ | Phiên bản API |

### Ví dụ Request
```json
{
  "data": {
    "request_payment_guide_id": 11,
    "partner_code": "NEXTLEND"
  },
  "checksum": "dfgdg456456456",
  "channel_code": "WEBPARTNER",
  "time_request": **********,
  "version": "1.0"
}
```

## Response

### Success Response (200)
```json
{
  "success": true,
  "result_code": 200,
  "message": "Success",
  "data": {
    "id": 11,
    "partner_code": "NEXTLEND",
    "type": 1,
    "profile_id": 0,
    "contract_code": "MPOS-*********-L2",
    "amount": 15000,
    "partner_request_id": "NLTC*********07",
    "payment_guide": "[{\"payment_method_code\":\"MPOS\",\"payment_channel_code\":\"MPOS\",\"payment_account_id\":\"********\",\"other_data\":[]}]",
    "list_fee": null,
    "other_data": null,
    "description": null,
    "status": 5,
    "created_by": "admin",
    "updated_by": null,
    "approved_by": null,
    "canceled_by": null,
    "create_request_payment_by": null,
    "amount_debited": 3000000,
    "status_debited": 1,
    "time_create_request_payment": **********,
    "time_expired": **********,
    "time_created": **********,
    "time_updated": **********,
    "time_approved": **********,
    "time_canceled": null,
    "request_payment_guide_merchant": {
      "id": 1,
      "merchant_id": "********",
      "request_payment_guide_id": 11,
      "merchant_name": "Nguyễn Văn A",
      "representation_name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      "mobile": "**********",
      "address": "18 Tam Trinh",
      "passport": "************",
      "other_data": "{\"ANH_HO_SO\": [], \"BAT_DONG_SAN\": [], \"TAI_SAN_KHAC\": [], \"THU_NHAP_KHAC\": [], \"THONG_TIN_CHU_HO\": [{\"label\": \"Họ tên\", \"value\": \"Nguyễn Văn A\"}], \"TONG_GIA_TRI_TAI_SAN\": [], \"THONG_TIN_HO_KINH_DOANH\": [], \"THU_NHAP_TU_HO_KINH_DOANH\": [], \"THONG_TIN_NGUOI_THAM_CHIEU\": []}",
      "time_created": 1751942842,
      "time_updated": null
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "result_code": 400,
  "message": "Không tìm thấy chỉ dẫn thanh toán"
}
```

## Chi tiết các trường dữ liệu

### RequestPaymentGuide Object
| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| `id` | integer | ID chỉ dẫn thanh toán |
| `partner_code` | string | Mã đối tác |
| `type` | integer | Loại chỉ dẫn (1: Thấu chi, 2: Khoản ứng) |
| `profile_id` | integer | ID hồ sơ |
| `contract_code` | string | Mã hợp đồng |
| `amount` | integer | Số tiền (VND) |
| `partner_request_id` | string | Mã yêu cầu đối tác |
| `payment_guide` | string | JSON string thông tin phương thức thanh toán |
| `list_fee` | string/null | Danh sách phí |
| `other_data` | string/null | Dữ liệu khác |
| `description` | string/null | Mô tả |
| `status` | integer | Trạng thái chỉ dẫn |
| `created_by` | string | Người tạo |
| `updated_by` | string/null | Người cập nhật |
| `approved_by` | string/null | Người duyệt |
| `canceled_by` | string/null | Người hủy |
| `create_request_payment_by` | string/null | Người tạo lệnh trích |
| `amount_debited` | integer | Số tiền đã trích |
| `status_debited` | integer | Trạng thái trích nợ |
| `time_create_request_payment` | integer/null | Thời gian tạo lệnh trích (timestamp) |
| `time_expired` | integer | Thời gian hết hạn (timestamp) |
| `time_created` | integer | Thời gian tạo (timestamp) |
| `time_updated` | integer/null | Thời gian cập nhật (timestamp) |
| `time_approved` | integer/null | Thời gian duyệt (timestamp) |
| `time_canceled` | integer/null | Thời gian hủy (timestamp) |

### RequestPaymentGuideMerchant Object
| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| `id` | integer | ID merchant |
| `merchant_id` | string | Mã merchant |
| `request_payment_guide_id` | integer | ID chỉ dẫn thanh toán |
| `merchant_name` | string | Tên merchant |
| `representation_name` | string | Tên người đại diện |
| `email` | string | Email |
| `mobile` | string | Số điện thoại |
| `address` | string | Địa chỉ |
| `passport` | string | Số CMND/CCCD |
| `other_data` | string | JSON string thông tin bổ sung |
| `time_created` | integer | Thời gian tạo (timestamp) |
| `time_updated` | integer/null | Thời gian cập nhật (timestamp) |

## Trạng thái (Status)
| Giá trị | Mô tả |
|---------|-------|
| 1 | Chờ duyệt |
| 2 | Đã duyệt |
| 3 | Đang xử lý |
| 4 | Đã tạo lệnh trích |
| 5 | Đã từ chối |

## Loại chỉ dẫn (Type)
| Giá trị | Mô tả |
|---------|-------|
| 1 | Thấu chi |
| 2 | Khoản ứng |

## Trạng thái trích nợ (Status Debited)
| Giá trị | Mô tả |
|---------|-------|
| 1 | Đang thu hộ |
| 2 | Đủ điều kiện đóng yêu cầu thu hộ |
| 3 | Đã đóng yêu cầu thu hộ |

## Lưu ý
- Các trường thời gian trả về dạng Unix timestamp.
- Trường `payment_guide` và `other_data` là JSON string, cần parse để sử dụng.
- Tham số `checksum` dùng để xác thực request, cần tuân thủ quy định bảo mật của hệ thống. 