<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\ForceCancelRequestPaymentMposAction;

use App\Lib\Helper;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentAction;
use App\Modules\CollectDebt\DTOs\RequestPaymentReceipt\CreateRequestPaymentReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction\CancelRequestPaymentAction;

class ForceCancelRequestPaymentMposAction
{

	public CheckRequestPaymentAction $action;

	public function __construct(CheckRequestPaymentAction $action)
	{
		$this->action = $action;
	}

	public function run()
	{
		$requestPaymentActions = RequestPaymentAction::query()
			->with('requestPayment')
			->where('action', CollectDebtEnum::RPA_ACTION_CANCEL)
			->where('status', CollectDebtEnum::RPA_STATUS_NEW)
			->orderBy('id', 'DESC')
			->get();

		if ($requestPaymentActions->isEmpty()) {
			return 'EMPTY';
		}

		$requestPaymentActions->each(function (RequestPaymentAction $requestPaymentAction) {
			$requestPayment = $requestPaymentAction->requestPayment;

			if (!$requestPayment) {
				$dataUpdated = ['tries' => 1, 'status' => CollectDebtEnum::RPA_STATUS_DONE];
				return $this->markAsDone($requestPaymentAction, $dataUpdated);
			}


			if ($requestPayment->isFinalStatusPayment()) {
				$dataUpdated = ['tries' => 1, 'status' => CollectDebtEnum::RPA_STATUS_DONE];
				return $this->markAsDone($requestPaymentAction, $dataUpdated);
			}

			// quá 3 lần thì thôi không cố
			if ($requestPaymentAction->tries >= 3) {
				$dataUpdated = ['tries' => 1, 'status' => CollectDebtEnum::RPA_STATUS_DONE];
				return $this->markAsDone($requestPaymentAction, $dataUpdated);
			}

			$checkResult = $this->action->executeCheck($requestPayment);
			$debitCmdStatus = $this->action->getDebitCmdStatus($checkResult);

			$dataUpdated = ['tries' => 1, 'status' => CollectDebtEnum::RPA_STATUS_DONE, 'data' => json_encode($checkResult)];

			switch ($debitCmdStatus) {
				// thu được rồi thì không tác động
				case 'APPROVED':
					$receipt = CreateRequestPaymentReceiptDTO::createReceipt($requestPayment, $this->action->getAmountApproved());
					return $this->markAsDone($requestPaymentAction, $dataUpdated);
					break;

				// đã hủy thì ko tác động
				case 'CANCEL':
					return $this->markAsDone($requestPaymentAction, $dataUpdated);
					break;

				// không tìm thấy lệnh trích -> đánh done
				case 'NOTFOUND':
					$wasUpdatedCancel = app(CancelRequestPaymentManualAction::class)->createReceipt(
						$requestPaymentAction->requestPayment, 
						'system'
					);
					
					return $this->markAsDone($requestPaymentAction, $dataUpdated);
					break;

				case 'TIMEOUT':
					// bỏ qua
					break;

				case 'EXPIRED':
					// bỏ qua
					break;

				// Tất cả các case khác thì gọi hủy để giải thoát
				default:
					$cancelResult = app(CancelRequestPaymentAction::class)->run($requestPayment);

					$dataUpdated['tries'] = ++$requestPaymentAction->tries;

					$dataUpdated['status'] = CollectDebtEnum::RPA_STATUS_NEW;

					if (CancelRequestPaymentAction::isCancelSuccess($cancelResult)) {
						$wasUpdatedCancel = app(CancelRequestPaymentManualAction::class)->createReceipt(
							$requestPaymentAction->requestPayment, 
							'system'
						);

						if (!$wasUpdatedCancel) {
							throw new BusinessException('Lỗi không đánh dấu được là hủy lệnh trích');
						}

						return $this->markAsDone($requestPaymentAction, $dataUpdated);
					}

					break;
			}
		});
	}


	public function markAsDone(RequestPaymentAction $requestPaymentAction, array $dataUpdated = []): RequestPaymentAction
	{
		$dataUpdated = array_merge($dataUpdated, [
			'time_updated' => now()->timestamp,
			'time_processed' => now()->timestamp,
			'updated_by' => Helper::getSystemUser(),
			'processed_by' => Helper::getSystemUser(),
		]);

		$requestPaymentAction->update($dataUpdated);

		return $requestPaymentAction;
	}
} // End class
