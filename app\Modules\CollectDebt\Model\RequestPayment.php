<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class RequestPayment extends Model
{
  protected $table = 'request_payment';
  public $timestamps = false;
  protected $appends = [];
  protected $guarded = [];

	// trạng thái thanh toán cuối cùng: đã nhận kết quả hoặc đã hủy
	public function isFinalStatusPayment(): bool {
		return $this->status_payment == CollectDebtEnum::RP_STT_PAYMENT_RECEIVED || 
					 $this->status_payment == CollectDebtEnum::RP_STT_PAYMENT_CANCELED;
	}

	public function isFinalStatus(): bool {
		return 	$this->status == CollectDebtEnum::RQ_STT_COMPLETED || 
						$this->status == CollectDebtEnum::RQ_STT_CANCELED;
	}

	public function isPaymentViaMpos(): bool {
		return $this->payment_method_code == 'MPOS';
	}

	public function isSentedRequest(): bool {
		return $this->status_payment == CollectDebtEnum::RP_STT_PAYMENT_SENDED;
	}

	public function requestPaymentGuide(): BelongsTo {
		return $this->belongsTo(RequestPaymentGuide::class, 'request_payment_guide_id', 'id');
	}

	public function requestPaymentReceipt(): HasOne {
		return $this->hasOne(RequestPaymentReceipt::class, 'partner_request_id', 'partner_request_id');
	}

	public function requestPaymentGuideMerchant(): BelongsTo {
		return $this->belongsTo(RequestPaymentGuideMerchant::class, 'request_payment_guide_id', 'request_payment_guide_id');
	}

	public function requestPaymentAction() {
		return $this->hasOne(RequestPaymentAction::class, 'request_payment_id', 'id')->whereIn('action', ['CREATE_VA', 'CREATE_IB']);
	}
} // End class
