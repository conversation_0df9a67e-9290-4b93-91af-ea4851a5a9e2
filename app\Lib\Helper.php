<?php

namespace App\Lib;

class Helper
{
	public static function priceFormat($price, string $unit = '')
	{
		$price = number_format($price, 0, '', ',');
		if (!empty($unit)) {
			$price .= $unit;
		}

		return $price;
	}

	public static function numberFormat($number = 0)
	{
		$number = (float) $number;

		$number = number_format($number, 0, '', '.');
		return $number;
	}

	public static function getSystemUser(): string
	{
		return 'system';
	}

	public static function jsonValidator($data)
  {
    if (!empty($data)) {
      return is_string($data) &&
        is_array(json_decode($data, true)) ? true : false;
    }
    return false;
  }
} // End class