<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\GetLogCashoutByGuideAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestCashoutLog;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\GetLogCashoutByGuideRequest;

class GetLogCashoutByGuideAction
{
	public function run(GetLogCashoutByGuideRequest $request)
	{
		$requestPaymentGuide = RequestPaymentGuide::query()->firstWhere([
			'partner_request_id' => $request->json('data.partner_request_id')
		]);

		if (!$requestPaymentGuide) {
			throw new BusinessException('Không tìm thấy thông tin yêu cầu thu hộ');
		}

		if ($requestPaymentGuide->partner_code != $request->json('data.partner_code')) {
			throw new BusinessException('Sai thông tin đối tác');
		}

		return [
			'cashout' => RequestPaymentCashout::query()->where(['request_payment_guide_id' => $requestPaymentGuide->id])->get(),
			'cashout_logs' => RequestCashoutLog::query()->where('request_payment_guide_id', $requestPaymentGuide->id)->get()
		];
	}
} // End class