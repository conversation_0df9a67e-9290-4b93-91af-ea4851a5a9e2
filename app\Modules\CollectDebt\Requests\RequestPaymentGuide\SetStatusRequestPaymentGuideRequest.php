<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;

class SetStatusRequestPaymentGuideRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['bail', 'required', 'array'],
			'data.partner_code' => ['bail', 'required', 'string', 'max:15'],
			'data.request_payment_guide_id' => ['bail', 'required', 'numeric', 'integer'],
			'data.status_code' => ['required', 'in:APPROVED,DENIED'],
			'data.username' => ['required', 'string', 'max:255'],
		];
	}

	public function messages()
	{
		return [];
	}
} // End class
