<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Modules\CollectDebt\Model\RequestPayment;

class CancelRequestPaymentDTO
{
	public $nextlend_request_id;
	public $merchantId;
	public $lendingRequestId;
	public $partner_code;
	public $_prefix;

	public function __construct(
		string $nextlendRequestId,
		string $merchantId,
		string $lendingRequestId,
		string $partnerCode = 'MPOS',
		string $prefix = ''
	) {
		$this->nextlend_request_id = $nextlendRequestId;
		$this->merchantId = $merchantId;
		$this->lendingRequestId = $lendingRequestId;
		$this->partner_code = $partnerCode;
		$this->_prefix = $prefix;
	}

	public static function fromRequestPayment(RequestPayment $requestPayment): self
	{
		return new self(
			sprintf('%s-%s', 
				config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE'), 
				$requestPayment->partner_request_id
			),
			$requestPayment->payment_account_id,
			$requestPayment->partner_request_id,
			$requestPayment->payment_method_code ?? 'MPOS',
			config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE')
		);
	}

	public function toArray(): array
	{
		return get_object_vars($this);
	}
} // End class
