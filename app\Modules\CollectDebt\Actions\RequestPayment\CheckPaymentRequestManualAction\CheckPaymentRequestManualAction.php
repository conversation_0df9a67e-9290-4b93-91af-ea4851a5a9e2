<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CheckPaymentRequestManualAction;

use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;

class CheckPaymentRequestManualAction
{
	public CheckRequestPaymentAction $action;

	public function __construct(CheckRequestPaymentAction $action)
	{
		$this->action = $action;
	}

	public function run(Request $request)
	{
		$requestPayment = RequestPayment::firstWhere([
			'partner_request_id' => $request->json('data.partner_request_id')
		]);

		if (!$requestPayment) {
			throw new BusinessException('Lệnh trích không tồn tại', 4004);
		}

		$checkResult = $this->action->executeCheck($requestPayment);

		if ($request->json('is_debit_result_applied', false)) {
			$this->action->handleCheckResult($requestPayment, $checkResult);
		}

		return $checkResult;
	}
} // End class
