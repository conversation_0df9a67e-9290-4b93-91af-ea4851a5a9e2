<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\ApproveRequestPaymentGuideAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\DTOs\RequestPayment\CreateRequestPaymentDTO;

class CreateRequestPaymentSubAction
{
	public function run(CreateRequestPaymentDTO $createRequestPaymentDTO): RequestPayment
	{
		$requestPayment = RequestPayment::query()->forceCreate($createRequestPaymentDTO->toArray());

		if (!$requestPayment) {
			throw new BusinessException('Lỗi không tạo được bản ghi lệnh trích nợ');
		}

		if ($requestPayment->payment_method_code == 'VIRTUALACCOUNT') {
			$wasUpdate = $requestPayment->update([
				'partner_request_id' => sprintf('NLVA%s%s', date('ymd'), $requestPayment->id)
			]);

			if (!$wasUpdate) {
				throw new BusinessException('Lỗi không cập nhật được partner_request_id');
			}
		}

		if ($requestPayment->payment_method_code == 'MPOS') {
			$wasUpdate = $requestPayment->update([
				'partner_request_id' => sprintf('NLMPOS%s%s', date('ymd'), $requestPayment->id)
			]);

			if (!$wasUpdate) {
				throw new BusinessException('Lỗi không cập nhật được partner_request_id');
			}
		}

		return $requestPayment;
	}
}
