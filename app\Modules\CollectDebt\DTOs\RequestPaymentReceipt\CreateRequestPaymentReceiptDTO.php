<?php

namespace App\Modules\CollectDebt\DTOs\RequestPaymentReceipt;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentReceipt;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\CreateRequestPaymentReceiptAction\CreateRequestPaymentReceiptAction;

class CreateRequestPaymentReceiptDTO
{
	public string $contract_code; //  Mã hợp đồng

	public string $payment_method_code; // Phương thức thu nợ

	public string $payment_channel_code; // Kênh thu nợ

	public string $payment_account_id; // Tài khoản thu nợ

	public string $partner_request_id; // Mã lệnh trích

	public string $partner_transaction_id; // Mã chứng từ của đối tác trích nợ

	public int $amount; // Số tiền ghi nhận thành công

	public int $time_created; // Thời điểm tạo

	public string $description = ''; // <PERSON><PERSON> tả nguồn tiền về

	public string $created_by = 'system';

	public int $status;

	public int $request_payment_guide_id;

	public string $partner_code;

	public function __construct(
		string $contract_code,
		string $payment_method_code,
		string $payment_channel_code,
		string $payment_account_id,
		string $partner_request_id,
		string $partner_transaction_id,
		float $amount,
		int $time_created,
		string $created_by,
		string $description = '',
		int $status = CollectDebtEnum::RP_RECEIPT_NEW,
		int $request_payment_guide_id,
		string $partner_code
	) {
		$this->contract_code = $contract_code;
		$this->payment_method_code = $payment_method_code;
		$this->payment_channel_code = $payment_channel_code;
		$this->payment_account_id = $payment_account_id;
		$this->partner_request_id = $partner_request_id;
		$this->partner_transaction_id = $partner_transaction_id;
		$this->amount = $amount;
		$this->time_created = $time_created;
		$this->created_by = $created_by;
		$this->description = $description;
		$this->status = $status;
		$this->request_payment_guide_id = $request_payment_guide_id;
		$this->partner_code = $partner_code;
	}

	public function toArray(): array
	{
		return [
			'contract_code' => $this->contract_code,
			'payment_method_code' => $this->payment_method_code,
			'payment_channel_code' => $this->payment_channel_code,
			'payment_account_id' => $this->payment_account_id,
			'partner_request_id' => $this->partner_request_id,
			'partner_transaction_id' => $this->partner_transaction_id,
			'amount' => $this->amount,
			'time_created' => $this->time_created,
			'created_by' => $this->created_by,
			'description' => $this->description,
			'status' => $this->status,
			'request_payment_guide_id' => $this->request_payment_guide_id,
			'partner_code' => $this->partner_code,
		];
	}

	public static function fromRequestPayment(RequestPayment $requestPayment, $amount, $status=1): self {
		$dto = new self(
			$requestPayment->contract_code,
			$requestPayment->payment_method_code,
			$requestPayment->payment_channel_code,
			$requestPayment->payment_account_id,
			$requestPayment->partner_request_id,
			$requestPayment->partner_transaction_id,
			$amount,
			now()->timestamp,
			$requestPayment->created_by ?? 'system',
			$requestPayment->description ?? '',
			$status,
			$requestPayment->request_payment_guide_id,
			$requestPayment->partner_code
		);

		return $dto;
	}

	public static function createReceipt(RequestPayment $requestPayment, $amount): RequestPaymentReceipt {
		$dto = self::fromRequestPayment($requestPayment, $amount, CollectDebtEnum::RP_RECEIPT_APPROVED);
		return app(CreateRequestPaymentReceiptAction::class)->run($dto);
	}
} // End class
