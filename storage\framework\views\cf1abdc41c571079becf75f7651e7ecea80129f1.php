<!DOCTYPE html>
<html lang="vi">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>NextLend <PERSON>hu <PERSON> - <PERSON></title>
	<style>
		body {
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			margin: 20px;
			background-color: #f5f5f5;
		}

		.container {
			max-width: 90%;
			margin: 0 auto;
			background: white;
			padding: 20px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		}

		h1 {
			color: #333;
			text-align: center;
			margin-bottom: 30px;
		}

		.status {
			margin: 10px 0;
			padding: 10px;
			border-radius: 4px;
			border-left: 4px solid #007bff;
			background-color: #f8f9fa;
		}

		.status.success {
			border-left-color: #28a745;
			background-color: #d4edda;
		}

		.status.error {
			border-left-color: #dc3545;
			background-color: #f8d7da;
		}

		.status.running {
			border-left-color: #ffc107;
			background-color: #fff3cd;
		}

		.progress {
			margin: 20px 0;
			padding: 15px;
			background-color: #e9ecef;
			border-radius: 4px;
		}

		.progress-bar {
			height: 20px;
			background-color: #007bff;
			border-radius: 4px;
			transition: width 0.3s ease;
		}



		.log {
			margin-top: 20px;
			padding: 15px;
			background-color: #f8f9fa;
			border-radius: 4px;
			max-height: 400px;
			overflow-y: auto;
			font-family: monospace;
			font-size: 12px;
		}
	</style>
	<script>
		const baseUrl = '<?php echo e($baseUrl); ?>';
	</script>
</head>

<body>
	<div class="container">
		<h1>🚀 NextLend Thu Hồi - Job Runner</h1>

		<div class="progress">
			<div class="progress-bar" id="progressBar" style="width: 0%"></div>
		</div>

		<div id="statusContainer"></div>

		<div class="log" id="logContainer"></div>
	</div>

	<script>
		let isRunning = false;
		let completedJobs = 0;
		let totalJobs = 0;

		const ENDPOINTS = [
			// Tạo yêu cầu từ nguồn tiền về
			"MakeRequestPaymentFromReceipt?orderByRaw=id%20asc",
			"MakeRequestPaymentFromReceipt?orderByRaw=id%20desc",

			// Duyệt yêu cầu thu hồi
			"ApproveRequestPaymentGuide?orderByRaw=id%20asc",
			"ApproveRequestPaymentGuide?orderByRaw=id%20desc",

			// Gửi yêu cầu sang đối tác
			"SendRequestPayment?orderByRaw=id%20asc",
			"SendRequestPayment?orderByRaw=id%20desc",

			// Kiểm tra lệnh trích đối tác
			"CheckRequestPayment",

			// Cutoff lệnh trích
			"CutOffRequestPayment?orderByRaw=id%20asc",
			"CutOffRequestPayment?orderByRaw=id%20desc",

			// Xử lý log notification
			"HandleNotiResultRequestPayment",

			// Đóng yêu cầu thu hồi
			"CloseRequestPaymentGuide?orderByRaw=id%20asc",

			// Hủy lệnh trích khi có nguồn tiền về VA
			"ForceCancelRequestPaymentMpos",

			// Tạo yêu cầu cashout
			"CreateCashout",

			// Đẩy yc cashout
			"PushCashout",

			// Kiểm tra yc cashout chủ động
			"CheckPayoutBulk",
		];

		function log(message, type = 'info') {
			const logContainer = document.getElementById('logContainer');
			const timestamp = new Date().toLocaleTimeString('vi-VN');
			const logEntry = document.createElement('div');
			logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
			logEntry.textContent = `[${timestamp}] ${message}`;
			logContainer.appendChild(logEntry);
			logContainer.scrollTop = logContainer.scrollHeight;
		}



		function updateProgress() {
			const progressBar = document.getElementById('progressBar');
			const percentage = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;
			progressBar.style.width = percentage + '%';
			progressBar.textContent = `${completedJobs}/${totalJobs} (${Math.round(percentage)}%)`;
		}

		function updateStatus(message, type = 'info') {
			const statusContainer = document.getElementById('statusContainer');
			const statusDiv = document.createElement('div');
			statusDiv.className = `status ${type}`;
			statusDiv.textContent = message;
			statusContainer.appendChild(statusDiv);
		}

		async function callUrl(path) {
			if (!isRunning) return;

			const fullUrl = `${baseUrl}/${path}`;

			log(`🔗 Đang gọi: ${fullUrl}`, 'info');

			try {
				const response = await fetch(fullUrl, {
					method: 'GET',
					headers: {
						'Accept': 'application/json',
						'Content-Type': 'application/json'
					}
				});

				if (response.ok) {
					const result = await response.text();
					log(`✅ Thành công: ${path}`, 'success');
					return {
						success: true,
						data: result
					};
				} else {
					log(`❌ Lỗi ${response.status}: ${path}`, 'error');
					return {
						success: false,
						error: `HTTP ${response.status}`
					};
				}
			} catch (error) {
				log(`❌ Lỗi kết nối: ${path} - ${error.message}`, 'error');
				return {
					success: false,
					error: error.message
				};
			}
		}

		async function runAllJobs() {
			if (isRunning) return;

			isRunning = true;
			completedJobs = 0;
			totalJobs = ENDPOINTS.length;

			log('🚀 Bắt đầu chạy tất cả jobs...', 'info');
			updateStatus('Đang chạy jobs...', 'running');
			updateProgress();

			const promises = ENDPOINTS.map(async (endpoint) => {
				if (!isRunning) return;

				const result = await callUrl(endpoint);
				completedJobs++;
				updateProgress();

				if (completedJobs === totalJobs) {
					finishJobs();
				}

				return result;
			});

			await Promise.all(promises);
		}

		function finishJobs() {
			isRunning = false;
			log('🎉 Tất cả jobs đã hoàn tất!', 'success');
			updateStatus('Tất cả jobs đã hoàn tất!', 'success');
		}

		// Khởi tạo và tự động chạy
		log('🚀 Tự động chạy jobs...', 'info');
		updateProgress();

		// Tự động chạy ngay khi load trang
		window.addEventListener('load', async function() {
			runAllJobs();
		});
	</script>
</body>

</html><?php /**PATH C:\laragon8\www\nextpay-web\request-debt-bidv\resources\views/jobs/index.blade.php ENDPATH**/ ?>