<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Modules\CollectDebt\Model\RequestPayment;

class SendVirtualAccountRequestDTO
{
	public string $orderType;
	public int $limitTrans;
	public string $nextlend_request_id;
	public string $email;
	public string $mcRequestId;
	public string $name;
	public string $phone;
	public float $equalAmount;
	public string $partner_code;
	public string $description;
	public int $expiryDate;

	public function __construct(
		string $orderType = 'VAOTHER',
		int $limitTrans = 1,
		string $nextlend_request_id = '',
		string $email = '',
		string $mcRequestId = '',
		string $name = '',
		string $phone = '',
		float $equalAmount = 0.0,
		string $partner_code = 'VIRTUALACCOUNT',
		string $description = 'Thanh toan TCNL',
		int $expiryDate = 0
	) {
		$this->orderType = $orderType;
		$this->limitTrans = $limitTrans;
		$this->nextlend_request_id = $nextlend_request_id;
		$this->email = $email;
		$this->mcRequestId = $mcRequestId;
		$this->name = $name;
		$this->phone = $phone;
		$this->equalAmount = $equalAmount;
		$this->partner_code = $partner_code;
		$this->description = $description;
		$this->expiryDate = $expiryDate;
	}

	public function toArray(): array
	{
		return [
			'orderType' => $this->orderType,
			'limitTrans' => $this->limitTrans,
			'nextlend_request_id' => $this->nextlend_request_id,
			'email' => $this->email,
			'mcRequestId' => $this->mcRequestId,
			'name' => $this->name,
			'phone' => $this->phone,
			'equalAmount' => $this->equalAmount,
			'partner_code' => $this->partner_code,
			'description' => $this->description,
			'expiryDate' => $this->expiryDate,
		];
	}

	public static function fromArray(array $data): self
	{
		return new self(
			$data['orderType'] ?? 'VAOTHER',
			$data['limitTrans'] ?? 1,
			$data['nextlend_request_id'] ?? '',
			$data['email'] ?? '',
			$data['mcRequestId'] ?? '',
			$data['name'] ?? '',
			$data['phone'] ?? '',
			$data['equalAmount'] ?? 0.0,
			$data['partner_code'] ?? 'VIRTUALACCOUNT',
			$data['description'] ?? 'Thanh toan TCNL',
			$data['expiryDate'] ?? 0
		);
	}

	public static function fromRequestPayment(RequestPayment $requestPayment): self
	{
		$merchant = $requestPayment->requestPaymentGuideMerchant;
		
		return new self(
			'VAOTHER', // orderType
			1, // limitTrans
			$requestPayment->contract_code,
			$merchant->email,
			$requestPayment->partner_request_id, 
			$merchant->merchant_name,
			$merchant->mobile,
			(float) $requestPayment->amount_request, // equalAmount
			'VIRTUALACCOUNT', // partner_code
			'Thanh toan TCNL', // description
			$requestPayment->time_expired // expiryDate
		);
	}
}
