<?php

namespace App\Modules\CollectDebt\Controllers\RequestPayment;

use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Resources\RequestPaymentResourceCollection;
use App\Modules\CollectDebt\Requests\RequestPayment\GetListRequestPaymentRequest;
use App\Modules\CollectDebt\Requests\RequestPayment\CancelRequestPaymentManualRequest;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SendRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CutOffRequestPaymentAction\CutOffRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\NotifyRequestPaymentAction\NotifyRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\ReportRequestPaymentAction\ReportRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\GetListRequestPaymentAction\GetListRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckPaymentRequestManualAction\CheckPaymentRequestManualAction;
use App\Modules\CollectDebt\Actions\RequestPayment\ForceCancelRequestPaymentMposAction\CancelRequestPaymentManualAction;
use App\Modules\CollectDebt\Actions\RequestPayment\ForceCancelRequestPaymentMposAction\ForceCancelRequestPaymentMposAction;
use App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\HandleNotiResultRequestPaymentAction;

class RequestPaymentController extends Controller
{
	public function GetListRequestPayment(GetListRequestPaymentRequest $request)
	{
		$result = app(GetListRequestPaymentAction::class)->run($request);
		$resource = new RequestPaymentResourceCollection($result);
		$response = $resource->toArray($request);
		return $this->successResponse($response);
	}

	public function GetDetailRequestPayment(Request $request)
	{
		$requestPayment = RequestPayment::firstWhere(['partner_request_id' => $request->json('partner_request_id')]);

		if (!$requestPayment) {
			throw new BusinessException('Lệnh trích không tồn tại', 4004);
		}

		return $this->successResponse($requestPayment);
	}

	public function SendRequestPayment()
	{
		$result = app(SendRequestPaymentAction::class)->run();
		return $this->successResponse($result);
	}

	public function CheckRequestPayment()
	{
		$result = app(CheckRequestPaymentAction::class)->run();
		return $this->successResponse($result);
	}

	public function CutOffRequestPayment()
	{
		$result = app(CutOffRequestPaymentAction::class)->run();
		return $this->successResponse($result);
	}

	public function CheckPaymentRequestManual(Request $request)
	{
		$result = app(CheckPaymentRequestManualAction::class)->run($request);
		return $this->successResponse($result);
	}

	public function NotifyRequestPayment(Request $request)
	{
		$requestPaymentLog = app(NotifyRequestPaymentAction::class)->run($request);
		return $this->successResponse($requestPaymentLog->only('id'));
	}

	public function HandleNotiResultRequestPayment()
	{
		$result = app(HandleNotiResultRequestPaymentAction::class)->run();
		return $this->successResponse($result);
	}

	public function ForceCancelRequestPaymentMpos()
	{
		$result = app(ForceCancelRequestPaymentMposAction::class)->run();
		return $this->successResponse($result);
	}

	public function CancelRequestPaymentManual(CancelRequestPaymentManualRequest $request)
	{
		$requestPayment = app(CancelRequestPaymentManualAction::class)->run($request);
		return $this->successResponse($requestPayment->only(['id']));
	}
} // End class