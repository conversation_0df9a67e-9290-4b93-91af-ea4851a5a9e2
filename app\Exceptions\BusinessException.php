<?php

namespace App\Exceptions;

use Exception;

class BusinessException extends Exception
{
	protected $errorCode;
	protected $httpCode;

	public function __construct(string $message = '', int $errorCode = 400, int $httpCode = 400)
	{
		parent::__construct($message);
		$this->errorCode = $errorCode;
		$this->httpCode = $httpCode;
	}

	public function getErrorCode(): int
	{
		return $this->errorCode;
	}

	public function getHttpCode(): int
	{
		return $this->httpCode;
	}

	public function render($request)
	{
		return response()->json([
			'success' => false,
			'error_code' => $this->errorCode,
			'message' => $this->getMessage(),
			'data' => (object) [],
			'errors' => (object) []
		], $this->httpCode);
	}
}
