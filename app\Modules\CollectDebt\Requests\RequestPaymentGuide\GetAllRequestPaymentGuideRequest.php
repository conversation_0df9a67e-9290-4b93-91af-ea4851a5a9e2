<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\CollectDebt\DTOs\RequestPaymentGuide\GetAllRequestPaymentGuideDTO;

class GetAllRequestPaymentGuideRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string'],
			'data.contract_code' => ['nullable', 'string'],
			'data.partner_request_id' => ['nullable', 'string'],
			'data.passport' => ['nullable', 'string'],
			'data.merchant_filter_keyword' => ['nullable', 'string'],
			'data.limit' => ['nullable', 'integer', 'min:1'],
			'data.page' => ['nullable', 'integer', 'min:1'],
			'data.field' => ['nullable', 'array'],
			'data.from_date' => ['nullable', 'string', 'date_format:Y-m-d'],
			'data.to_date' => ['nullable', 'string', 'date_format:Y-m-d', 'after_or_equal:data.from_date'],
		];
	}

	public function messages()
	{
		return [];
	}

	public function toDTO()
	{
		$data = $this->input('data', []);
		return new GetAllRequestPaymentGuideDTO(
			$data['contract_code'] ?? null,
			$data['partner_code'] ?? null,
			$data['partner_request_id'] ?? null,
			$data['passport'] ?? null,
			$data['merchant_filter_keyword'] ?? null,
			$data['from_date'] ?? null,
			$data['to_date'] ?? null,
			$data['status'] ?? null
		);
	}
} // End class
