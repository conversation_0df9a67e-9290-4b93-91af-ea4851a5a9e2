<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Modules\CollectDebt\Model\RequestPayment;

class CheckVirtualAccountRequestDTO
{
	public string $contractCode;
	public string $mcRequestId;
	public string $partnerCode;

	public function __construct(string $contractCode, string $mcRequestId, string $partnerCode = 'VIRTUALACCOUNT')
	{
		$this->contractCode = $contractCode;
		$this->mcRequestId = $mcRequestId;
		$this->partnerCode = $partnerCode;
	}

	public function toArray(): array
	{
		return [
			'nextlend_request_id' => $this->contractCode,
			'mcRequestId' => $this->mcRequestId,
			'partner_code' => $this->partnerCode,
		];
	}

	public static function fromRequestPayment(RequestPayment $requestPayment): self
	{
		return new self(
			$requestPayment->contract_code,
			$requestPayment->partner_request_id,
			'VIRTUALACCOUNT'
		);
	}
}
