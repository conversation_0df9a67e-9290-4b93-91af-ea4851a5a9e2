# API GetAllRequestPaymentGuide

## Tổng quan
API này được sử dụng để lấy danh sách chỉ dẫn thanh toán (Request Payment Guide) với phân trang và các bộ lọc tùy chọn.

## Thông tin cơ bản
- **URL**: `/GetAllRequestPaymentGuide`
- **Method**: `GET`
- **Content-Type**: `application/json`

## Request

### Headers
```
Content-Type: application/json
Authorization: Bearer {token}
```

### Body Parameters

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| `data.partner_code` | string | ✅ | Mã đối tác (VD: NEXTLEND, TNEX) |
| `data.contract_code` | string | ❌ | Mã hợp đồng để lọc |
| `data.partner_request_id` | string | ❌ | Mã yêu cầu đối tác để lọc |
| `data.passport` | string | ❌ | Số CMND/CCCD để lọc theo thông tin merchant |
| `data.email` | string | ❌ | Email để lọc theo thông tin merchant |
| `data.mobile` | string | ❌ | Số điện thoại để lọc theo thông tin merchant |
| `data.merchant_name` | string | ❌ | Tên merchant để lọc |
| `data.limit` | integer | ❌ | Số lượng bản ghi mỗi trang (mặc định: 20) |
| `data.page` | integer | ❌ | Trang hiện tại (mặc định: 1) |
| `data.field` | array | ❌ | Các trường cần lấy (mặc định: ['*']) |
| `checksum` | string | ✅ | Checksum xác thực request |
| `channel_code` | string | ✅ | Kênh gửi request (VD: WEBPARTNER) |
| `time_request` | integer | ✅ | Thời gian gửi request (timestamp) |
| `version` | string | ✅ | Phiên bản API |

### Ví dụ Request
```json
{
  "data": {
    "contract_code": "",
    "partner_code": "NEXTLEND",
    "partner_request_id": "",
    "passport": "",
    "email": "",
    "mobile": "",
    "merchant_name": "",
    "limit": 20,
    "page": 1,
    "field": ["*"]
  },
  "checksum": "dfgdg456456456",
  "channel_code": "WEBPARTNER",
  "time_request": 1732260044,
  "version": "1.0"
}
```

## Response

### Success Response (200)

#### Cấu trúc Response
```json
{
  "success": true,
  "result_code": 200,
  "message": "Success",
  "data": {
    "data": [
      {
        "id": 17,
        "partner_code": "NEXTLEND",
        "type": 1,
        "profile_id": ********,
        "contract_code": "MPOS-*********-L7",
        "amount": 15000,
        "partner_request_id": "NLTC*********11",
        "payment_guide": "[{\"payment_method_code\":\"MPOS\",\"payment_channel_code\":\"MPOS\",\"payment_account_id\":\"********\",\"other_data\":[]}]",
        "list_fee": null,
        "other_data": null,
        "description": null,
        "status": 1,
        "created_by": "admin",
        "updated_by": null,
        "approved_by": null,
        "canceled_by": null,
        "create_request_payment_by": null,
        "amount_debited": 0,
        "status_debited": 1,
        "time_create_request_payment": null,
        "time_expired": **********,
        "time_created": **********,
        "time_updated": null,
        "time_approved": null,
        "time_canceled": null,
        "request_payment_guide_merchant": {
          "id": 3,
          "request_payment_guide_id": 17,
          "merchant_id": "********",
          "merchant_name": "Nguyễn Văn A",
          "representation_name": "Nguyễn Văn A",
          "email": "<EMAIL>",
          "mobile": "**********",
          "address": "18 Tam Trinh",
          "passport": "*********012"
        }
      }
    ],
    "meta": {
      "current_page": 1,
      "from": 1,
      "last_page": 1,
      "per_page": 20,
      "to": 6,
      "total": 6
    }
  }
}
```

### Error Response

#### 400 - Bad Request
```json
{
  "success": false,
  "result_code": 400,
  "message": "Không xác minh được thông tin đối tác"
}
```

## Chi tiết các trường dữ liệu

### RequestPaymentGuide Object

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| `id` | integer | ID của chỉ dẫn thanh toán |
| `partner_code` | string | Mã đối tác |
| `type` | integer | Loại chỉ dẫn (1: Thấu chi, 2: Khoản ứng) |
| `profile_id` | integer | ID hồ sơ |
| `contract_code` | string | Mã hợp đồng |
| `amount` | integer | Số tiền (VND) |
| `partner_request_id` | string | Mã yêu cầu đối tác |
| `payment_guide` | string | JSON string chứa thông tin phương thức thanh toán |
| `list_fee` | string/null | Danh sách phí |
| `other_data` | string/null | Dữ liệu khác |
| `description` | string/null | Mô tả |
| `status` | integer | Trạng thái chỉ dẫn |
| `created_by` | string | Người tạo |
| `updated_by` | string/null | Người cập nhật |
| `approved_by` | string/null | Người duyệt |
| `canceled_by` | string/null | Người hủy |
| `create_request_payment_by` | string/null | Người tạo lệnh trích |
| `amount_debited` | integer | Số tiền đã trích |
| `status_debited` | integer | Trạng thái trích nợ |
| `time_create_request_payment` | integer/null | Thời gian tạo lệnh trích (timestamp) |
| `time_expired` | integer | Thời gian hết hạn (timestamp) |
| `time_created` | integer | Thời gian tạo (timestamp) |
| `time_updated` | integer/null | Thời gian cập nhật (timestamp) |
| `time_approved` | integer/null | Thời gian duyệt (timestamp) |
| `time_canceled` | integer/null | Thời gian hủy (timestamp) |

### RequestPaymentGuideMerchant Object

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| `id` | integer | ID của merchant |
| `request_payment_guide_id` | integer | ID chỉ dẫn thanh toán |
| `merchant_id` | string | Mã merchant |
| `merchant_name` | string | Tên merchant |
| `representation_name` | string | Tên người đại diện |
| `email` | string | Email |
| `mobile` | string | Số điện thoại |
| `address` | string | Địa chỉ |
| `passport` | string | Số CMND/CCCD |

### Meta Object (Phân trang)

| Trường | Kiểu dữ liệu | Mô tả |
|--------|--------------|-------|
| `current_page` | integer | Trang hiện tại |
| `from` | integer | Bản ghi đầu tiên |
| `last_page` | integer | Trang cuối cùng |
| `per_page` | integer | Số bản ghi mỗi trang |
| `to` | integer | Bản ghi cuối cùng |
| `total` | integer | Tổng số bản ghi |

## Trạng thái (Status)

| Giá trị | Mô tả |
|---------|-------|
| 1 | Chờ duyệt |
| 2 | Đã duyệt |
| 3 | Đang xử lý |
| 4 | Đã tạo lệnh trích |
| 5 | Đã từ chối |

## Loại chỉ dẫn (Type)

| Giá trị | Mô tả |
|---------|-------|
| 1 | Thấu chi |
| 2 | Khoản ứng |

## Trạng thái trích nợ (Status Debited)

| Giá trị | Mô tả |
|---------|-------|
| 1 | Đang thu hộ |
| 2 | Đủ điều kiện đóng yêu cầu thu hộ |
| 3 | Đã đóng yêu cầu thu hộ |

## Lưu ý

1. **Bắt buộc**: Tham số `data.partner_code` là bắt buộc và phải được cung cấp trong mọi request.
2. **Phân trang**: API hỗ trợ phân trang với các tham số `data.limit` và `data.page` (nằm trong `data`).
3. **Lọc dữ liệu**: Có thể lọc theo nhiều tiêu chí khác nhau như contract_code, partner_request_id, thông tin merchant.
4. **Timestamp**: Các trường thời gian được trả về dưới dạng Unix timestamp.
5. **Payment Guide**: Trường `payment_guide` chứa JSON string với thông tin chi tiết về phương thức thanh toán.
6. **Checksum**: Tham số `checksum` dùng để xác thực request, cần tuân thủ quy định bảo mật của hệ thống. 