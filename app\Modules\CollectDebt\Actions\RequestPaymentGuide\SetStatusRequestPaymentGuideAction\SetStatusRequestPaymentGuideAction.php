<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\SetStatusRequestPaymentGuideAction;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentGuideMerchant;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\SetStatusRequestPaymentGuideRequest;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\CreateRequestPaymentGuideBulkRequest;

class SetStatusRequestPaymentGuideAction
{
	public function run(SetStatusRequestPaymentGuideRequest $request)
	{
		$requestPaymentGuide = RequestPaymentGuide::query()
		->with('requestPaymentGuideMerchant')
		->firstWhere([
			'id' => $request->json('data.request_payment_guide_id'),
			'partner_code' => $request->json('data.partner_code'),
		]);

		if (!$requestPaymentGuide) {
			throw new BusinessException('Không tìm thấy yêu cầu thu hộ');
		}

		throw_if(
			$requestPaymentGuide->status != CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NEW, 
			new BusinessException('Yêu cầu thu hộ đang có trạng thái không phù hợp để Duyệt hoặc từ chối')
		);
		
		$requestPaymentGuide->time_updated = now()->timestamp;
		if ($request->json('data.status_code') == 'APPROVED') {
			
			if (!$requestPaymentGuide->requestPaymentGuideMerchant) {
				throw new BusinessException('Yêu cầu thu hộ chưa có thông tin MC. Không thể duyệt');
			}

			$requestPaymentGuide->status = CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED;
			$requestPaymentGuide->time_approved = now()->timestamp;
			$requestPaymentGuide->approved_by = $request->json('data.username');
		}
		
		if ($request->json('data.status_code') == 'DENIED'){
			$requestPaymentGuide->status = CollectDebtEnum::REQUEST_PAYMENT_GUIDE_DENIED;
			$requestPaymentGuide->canceled_by = $request->json('data.username');
			$requestPaymentGuide->time_canceled = now()->timestamp;
		}

		$r = $requestPaymentGuide->save();

		if (!$r) {
			throw new BusinessException('Lỗi khi cập nhật trạng thái yêu cầu thu hộ');
		}

		return $requestPaymentGuide;
	}
} // End class
