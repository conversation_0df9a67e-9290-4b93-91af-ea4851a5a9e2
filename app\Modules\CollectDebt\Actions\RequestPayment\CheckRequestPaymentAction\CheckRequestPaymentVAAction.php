<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\DTOs\RequestPayment\CheckVirtualAccountRequestDTO;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;

class CheckRequestPaymentVAAction
{
	public NextLendServiceConnection $connection;

	public function __construct(NextLendServiceConnection $connection)
	{
		$this->connection = $connection;
	}

	/**
		* array:8 [
			"Fnc" => "vACheckOrder"
			"Version" => "1.0"
			"ChannelCode" => "THUHO"
			"RespCode" => "00"
			"data" => "{"error_code":"00","error_message":"Thành công","checksum":"0a310a8fcda1d0201e425bb2a48d8d45","data":{"merchantId":5,"mcRequestId":"THUHO-NLVA25071184","bankCode":"","mcRequestAccNumber":"","vaNextpayNumber":"NPAPHEMPQUR7JEC","vaBankNumber":"962NPD6290153835089","vaReference":"ODG7VJKOTTKKQNP","name":"PHUONG HUU COFFEE","limitTrans":1,"maxAmount":0,"minAmount":0,"equalAmount":********,"detail1":"","detail2":"","detail3":"","email":"<EMAIL>","phoneNumber":"**********","expiryDate":**********,"regNumber":"","vaStatus":"ACTIVED","qrCode":"00020101021238630010A000000727013300069704180119962NPD62901538350890208QRIBFTTA53037045408********5802VN62350831ODG7VJKOTTKKQNP Thanh toan TCNL63041697","qrImage":"","orderStatus":"SUCCESS","paymentTime":**********,"orderType":"VAOTHER","description":"Thanh toan TCNL","transferDesc":"ODG7VJKOTTKKQNP Thanh toan TCNL","npTransId":"TRXKCWPKTQNKUT2T","fromAccountName":"Trần Thị Hoài Thanh","fromAccountNumber":"*********","toAccountName":"PHUONG HUU COFFEE","toAccountNumber":"962NPD6290153835089","reMark":"ODG7VJKOTTKKQNP Thanh toan TCNL","transDate":**********,"providerRespId":"***********"}}"
			"total" => 0
			"Checksum" => "4a7805b3561ef0d84ef454b55adea46f"
			"Description" => "success"
		]
	 */
	public function executeCheck(RequestPayment $requestPayment)
	{
		$dto = CheckVirtualAccountRequestDTO::fromRequestPayment($requestPayment);
		$params = $dto->toArray();
		$result = $this->connection->callRequest($params, 'vACheckOrder', 'POST');

		$requestPaymentLog = RequestPaymentLog::query()->forceCreate([
			'contract_code' => $requestPayment->contract_code,
			'partner_request_id' => $requestPayment->partner_request_id,
			'func' => 'check_va',
			'request' => json_encode($params),
			'response' => json_encode($result),
		]);
		
		return $result;
	}

	// Tồn tại VA
	public function isExistVA(array $result): bool
	{
		return isset($result['RespCode']) && $result['RespCode'] === '00';
	}

	// VA không tồn tại
	public function isNotFound(array $result): bool {
		return $result['RespCode'] === '07';
	}

	// VA đã được thanh toán
	public function isPaidVA(array $result): bool
	{
		$data = json_decode($result['data'], true);
		return $this->isExistVA($result) && isset($data['data']['orderStatus']) 
																		 && $data['data']['orderStatus'] === 'SUCCESS';
	}
} // End class
