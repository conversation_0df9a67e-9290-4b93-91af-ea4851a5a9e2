<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentCashout;

use Illuminate\Foundation\Http\FormRequest;

class GetAllCashoutRecordRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string'],
			'data.partner_request_id' => ['nullable', 'string'],
			'data.from_date' => ['nullable', 'date_format:Y-m-d'],
			'data.to_date' => ['nullable', 'date_format:Y-m-d', 'after:data.time_created_from'],
			'data.status' => ['nullable', 'numeric', 'integer', 'min:1'],
		];
	}

	public function messages()
	{
		return [];
	}

} // End class
