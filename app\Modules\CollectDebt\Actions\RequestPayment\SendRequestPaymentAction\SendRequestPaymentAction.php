<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction;

use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentVAAction;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\SendRequestViaVirtalAccountSubAction;
use App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction\XuLyLenhTrichVABiTrungSubAction;

class SendRequestPaymentAction
{
	private array $__processedIds = [];

	private array $__excludedIds = [];

	public function run()
	{
		try {
			for ($i = 1; $i < 40; $i++) {

				$requestPayment = $this->handlePaymentSending();
				if ($requestPayment == 'EMPTY') {
					break;
				}

				$this->__processedIds[] = $requestPayment->id;
			}
		} catch (\Throwable $th) {
			throw $th;
		}

		return $this->__processedIds;
	}

	public function handlePaymentSending()
	{
		$requestPayment = RequestPayment::query()
			->whereRaw('(status_payment = ? OR status_payment = ?)', [CollectDebtEnum::RP_STT_PAYMENT_UNSEND, CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR])
			->whereIn('payment_method_code', ['MPOS', 'VIRTUALACCOUNT']);

		if (!empty($this->__excludedIds)) {
			$requestPayment = $requestPayment->whereNotIn('id', $this->__excludedIds);
		}

		$requestPayment = $requestPayment->orderBy('status_payment', 'ASC')
			->orderByRaw(request('orderByRaw', 'id asc'))
			->first();

		if (!$requestPayment) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $requestPayment->id;

		$wasUpdateSending = $requestPayment->where('id', $requestPayment->id)
			->whereRaw('(status_payment = ? OR status_payment = ?)', [
				CollectDebtEnum::RP_STT_PAYMENT_UNSEND,
				CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR
			])
			->update([
				'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDING,
			]);

		if (!$wasUpdateSending) {
			throw new BusinessException('Loi khong update trang thai gui lenh thanh dang gui');
		}

		DB::beginTransaction();
		try {
			if ($requestPayment->payment_method_code == 'MPOS') {
				// Gọi hàm gửi lệnh trích
				$r = app(SendRequestViaMposSubAction::class)->run($requestPayment);
			}
			
			if ($requestPayment->payment_method_code == 'VIRTUALACCOUNT') {
				// Kiểm tra nếu có time_sended thì gọi check trước, không có mới gọi create
				if (!empty($requestPayment->time_sended)) {
					$checkVaResult = app(CheckRequestPaymentVAAction::class)->executeCheck($requestPayment);
					app(XuLyLenhTrichVABiTrungSubAction::class)->run($requestPayment, $checkVaResult);
				} else {
					$r = app(SendRequestViaVirtalAccountSubAction::class)->run($requestPayment);
				}
			}
			
			DB::commit();
			return $requestPayment;
		} catch (\Throwable $th) {
			DB::rollBack();

			$wasUpdateUnseding = RequestPayment::query()->where([
				'id' => $requestPayment->id,
				'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDING,
			])->update([
				'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR,
				'time_updated' => now()->timestamp,
				'time_sended' => now()->timestamp,
			]);

			throw $th;
		} 
	}
} // End class