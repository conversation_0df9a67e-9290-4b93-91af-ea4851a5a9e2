<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\GetAllRequestPaymentGuideAction;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class GetAllRequestPaymentGuideAction
{
	public function run(Request $request)
	{
		$requestPaymentGuides = RequestPaymentGuide::query();

		if (empty($request->json('data.partner_code'))) {
			throw new BusinessException('Không xác minh được thông tin đối tác');
		}

		if (!empty($request->json('data.status'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('status', $request->json('data.status'));
		}

		if (!empty($request->json('data.contract_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('contract_code', $request->json('data.contract_code'));
		}

		if (!empty($request->json('data.partner_contract_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_contract_code', $request->json('data.partner_contract_code'));
		}

		if (!empty($request->json('data.partner_code'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_code', $request->json('data.partner_code'));
		}

		if (!empty($request->json('data.partner_request_id'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('partner_request_id', $request->json('data.partner_request_id'));
		}

		if (!empty($request->json('data.passport'))) {
			$requestPaymentGuides = $requestPaymentGuides->whereHas('requestPaymentGuideMerchant', function ($query) use ($request) {
				$query->where('passport', trim($request->json('data.passport')));
			});
		}

		if (!empty($request->json('data.from_date'))) {
			$fromDate = Carbon::parse($request->json('data.from_date'))->startOfDay()->timestamp;
			$requestPaymentGuides = $requestPaymentGuides->where('time_created', '>=', $fromDate);
		}

		if (!empty($request->json('data.to_date'))) {
			$toDate = Carbon::parse($request->json('data.to_date'))->endOfDay()->timestamp;
			$requestPaymentGuides = $requestPaymentGuides->where('time_created', '<=', $toDate);
		}

		if (!empty($request->json('data.type'))) {
			$requestPaymentGuides = $requestPaymentGuides->where('type', $request->json('data.type'));
		}

		$keyword = trim($request->json('data.merchant_filter_keyword', ''));

		if (!empty($keyword)) {
			$requestPaymentGuides = $requestPaymentGuides->whereHas('requestPaymentGuideMerchant', function ($query) use ($keyword) {
				$query->where('merchant_name', 'LIKE', $keyword.'%')
				      ->orWhere('email', 'LIKE', $keyword.'%')
				      ->orWhere('mobile', 'LIKE', $keyword.'%');
			});
		}


		$requestPaymentGuides = $requestPaymentGuides->with('requestPaymentGuideMerchant:id,request_payment_guide_id,merchant_id,merchant_name,representation_name,email,mobile,address,passport')
		->latest('id')->paginate(
			$request->json('data.limit', 20),
			$request->json('data.field', ['*']),
			'page',
			$request->json('data.page', 1)
		);

		return $requestPaymentGuides;
	}
} // End class