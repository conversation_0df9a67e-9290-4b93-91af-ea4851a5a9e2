<?php

namespace App\Modules\CollectDebt\DTOs\RequestPaymentGuide;

class GetAllRequestPaymentGuideDTO
{
	public $contract_code;
	public $partner_code;
	public $partner_request_id;
	public $passport;
	public $merchant_filter_keyword;
	public $from_date;
	public $to_date;
	public $status;

	public function __construct($contract_code = null, $partner_code = null, $partner_request_id = null, $passport = null, $merchant_filter_keyword = null, $from_date = null, $to_date = null, $status = null)
	{
		$this->contract_code = $contract_code;
		$this->partner_code = $partner_code;
		$this->partner_request_id = $partner_request_id;
		$this->passport = $passport;
		$this->merchant_filter_keyword = $merchant_filter_keyword;
		$this->from_date = $from_date;
		$this->to_date = $to_date;
		$this->status = $status;
	}
}
