<?php

namespace App\Modules\CollectDebtGateway\Repositories\Connections;

use App\Lib\Encryption;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class NextLendServiceConnection
{
	protected string $_API_URL;
	protected string $_API_APP_SECRET_KEY;
	protected string $_API_KET_ENCRYPTION_DATA;
	protected string $_version;
	protected string $_channelCode;

	public function __construct()
	{
		$this->_API_URL = config('nextlend.NEXTLEND_SERVICE_REQUEST_URL');
		$this->_API_APP_SECRET_KEY = config('nextlend.NEXTLEND_SERVICE_SECRET_KEY');
		$this->_API_KET_ENCRYPTION_DATA = config('nextlend.NEXTLEND_SERVICE_ENCRYPT_KEY');
		$this->_version = config('nextlend.NEXTLEND_SERVICE_VERSION');
		$this->_channelCode = config('nextlend.NEXTLEND_SERVICE_CHANNEL_CODE');
	}

	public function callRequest(array $inputs = [], string $functionName = '', string $method = 'POST')
	{
		$requestInput = $this->_makeRequestInput($inputs, $functionName);

		$dataPost = json_encode($requestInput, JSON_UNESCAPED_UNICODE);

		$headerArray = array(
			"cache-control: no-cache",
			"content-type: application/json; charset=UTF-8",
		);

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => $this->_API_URL,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_HTTPAUTH => CURLAUTH_ANY,
			CURLOPT_SSL_VERIFYPEER => false,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_CUSTOMREQUEST => $method,
			CURLOPT_POSTFIELDS => $dataPost,
			CURLOPT_TIMEOUT => 15,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_HTTPHEADER => $headerArray
		));

		$response = curl_exec($curl);

		Log::info($functionName . 'Response: ' . $response);

		$resultStatus = curl_getinfo($curl);
		curl_close($curl);

		if ($resultStatus['http_code'] == 200) {
      return json_decode($response, true);
    }

    return false;
	}

	public function _makeRequestInput(array $params = [], string $func = ''): array
	{
		$hash          = $this->_buildHash($params);
		$chuoiTruocMd5 = $func . $this->_version . $this->_channelCode . $hash . $this->_API_APP_SECRET_KEY;
		$checkSum      = md5($chuoiTruocMd5);

		$input = [
			'Fnc' => $func,
			'Checksum' => $checkSum,
			'EncData' => $hash,
			'ChannelCode' => $this->_channelCode,
			'Version' => $this->_version,
		];

		Log::info($func . 'Input', $input);
		return $input;
	}

	protected function _buildHash(array $params = []): string
	{
		$paramsJsonEncode = json_encode($params, JSON_UNESCAPED_UNICODE);
		$paramsEncrypt = Encryption::Encrypt($paramsJsonEncode, $this->_API_KET_ENCRYPTION_DATA);
		return $paramsEncrypt;
	}
} // End class