<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;

class CheckTokenMiddleware
{
	public function handle($request, Closure $next)
	{
		if (config('nextlend.APP_DEBUG_CHECK_SUM') == 0) {
			return $next($request);
		}

		$secretKey = config('nextlend.CHECKSUMKEY');

		if ($request->json('data.channel_code') == 'WEBBACKEND_PARTNER') {
			$secretKey = config('nextlend.WEBBACKEND_PARTNER_CHECKSUMKEY');
		}

		$token = $request->json('checksum');
		$tokenTime = $request->json('time_request');
		$Version = $request->json('version');
		$ChannelCode = $request->json('channel_code');
		$data = $request->json('data');

		$dataJson = json_encode($data, JSON_UNESCAPED_UNICODE);
		$dataJson = str_replace("null", '""', $dataJson);
		$checksumString = $dataJson . $Version . $ChannelCode . $tokenTime . $secretKey;


		$checksum = md5($checksumString);

		throw_if(empty($token) || empty($tokenTime), new BusinessException('checksum is required'));
		
		if ($checksum != $token) {
			Log::error("checksum string must be:" . $checksumString);
			throw new BusinessException('checksum is invalid');
		}

		return $next($request);
	}

	public function terminate($request, $response) {}
}
