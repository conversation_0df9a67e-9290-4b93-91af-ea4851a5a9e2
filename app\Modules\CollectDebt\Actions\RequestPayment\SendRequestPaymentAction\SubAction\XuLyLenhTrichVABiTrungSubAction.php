<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentVAAction;

class XuLyLenhTrichVABiTrungSubAction
{
	public CheckRequestPaymentVAAction $action;

	public function __construct(CheckRequestPaymentVAAction $action)
	{
		$this->action = $action;
	}

	public function run(RequestPayment $requestPayment, array $checkVaResult)
	{
		// VA chưa được tạo -> thực hiện tạo mới
		if ($this->action->isNotFound($checkVaResult)) {
			$wasUpdateUnsend = $requestPayment->update([
				'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_UNSEND,
				'time_updated' => now()->timestamp,
			]);

			if (!$wasUpdateUnsend) {
				throw new BusinessException('Lỗi không cập nhật lệnh trích là chưa gửi');
			}

			return $requestPayment;
		}

		// VA đã được tạo
		if ($this->action->isExistVA($checkVaResult)) {
			// Update lại mã chứng từ của lệnh trích hiện tại
			$wasUpdatePartnerTransactionId = $requestPayment->update([
				'partner_transaction_id' => $checkVaResult['data']['vaNextpayNumber'],
				'time_sended' => now()->timestamp,
				'status' => CollectDebtEnum::RQ_STT_APPROVED,
				'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDED,
			]);

			if (!$wasUpdatePartnerTransactionId) {
				throw new BusinessException('Lỗi không cập nhật được mã chứng từ của lệnh trích hiện tại');
			}

			return $requestPayment;
		}

		throw new BusinessException('Lỗi không xử lý được lệnh trích VA chưa rõ nguyên nhân');
	}
} // End class
