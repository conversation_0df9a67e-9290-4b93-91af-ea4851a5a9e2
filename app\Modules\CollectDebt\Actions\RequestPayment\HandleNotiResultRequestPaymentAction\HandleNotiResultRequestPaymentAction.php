<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\DetailRequestPaymentAction\DetailRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\SubAction\HandleVANotiSubAction;
use App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\SubAction\HandleMposNotiSubAction;

class HandleNotiResultRequestPaymentAction
{
	public CheckRequestPaymentAction $checkRequestPaymentAction;

	private array $__processedIds = [];

	public function __construct(CheckRequestPaymentAction $checkRequestPaymentAction)
	{
		$this->checkRequestPaymentAction = $checkRequestPaymentAction;
	}

	public function run()
	{
		$requestPaymentLogs = RequestPaymentLog::query()
																					 ->where('status', CollectDebtEnum::RQP_LOG_STT_NEW)
																					 ->where('func', 'notify')
																					 ->orderBy('id', 'asc')
																					 ->limit(30)
																					 ->select([
																						'id',
																						'partner_request_id',
																						'status',
																						'updated_at',
																						'response'
																					 ])
																					 ->get();
		if ($requestPaymentLogs->isEmpty())	 {
			return 'EMPTY';
		}

		$requestPaymentLogs->each(function (RequestPaymentLog $requestPaymentLog) {
			$responseJson = json_decode($requestPaymentLog->response, true);
		
			$paymentMethodCode = $responseJson['data']['payment_channel_code'] ?? '';
			
			if ($paymentMethodCode == 'MPOS') {
				return app(HandleMposNotiSubAction::class)->run($requestPaymentLog, $responseJson);
			}

			if ($paymentMethodCode == 'VIRTUALACCOUNT') {
				return app(HandleVANotiSubAction::class)->run($requestPaymentLog, $responseJson);
			}
		});

		return $this->__processedIds;
	}

	public function markAsDone(RequestPaymentLog $requestPaymentLog): RequestPaymentLog
	{
		$requestPaymentLog->status = CollectDebtEnum::RQP_LOG_STT_COMPLETED;
		$requestPaymentLog->updated_at = now();
		$requestPaymentLog->save();

		$this->__processedIds[] = $requestPaymentLog->id;
		return $requestPaymentLog;
	}
} // End class
