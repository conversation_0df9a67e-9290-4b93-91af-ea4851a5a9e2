<?php

namespace App\Modules\CollectDebt\Requests\RequestPayment;

use Illuminate\Foundation\Http\FormRequest;

class CancelRequestPaymentManualRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string', 'max:20'],
			'data.partner_request_id' => ['required', 'string', 'max:50'],
			'data.username' => ['required', 'string', 'max:255'],
		];
	}

	public function messages()
	{
		return [
			'data.partner_code.required' => 'Mã đối tác không được để trống',
			'data.partner_code.string' => 'Mã đối tác phải là chuỗi',
			'data.partner_code.max' => 'Mã đối tác không được vượt quá 20 ký tự',
			'data.partner_request_id.required' => 'Mã yêu cầu thu hộ không được để trống',
			'data.partner_request_id.string' => 'Mã yêu cầu thu hộ phải là chuỗi',
			'data.partner_request_id.max' => 'Mã yêu cầu thu hộ không được vượt quá 50 ký tự',
			'data.username.required' => 'Tên người dùng không được để trống',
		];
	}
} // End class
