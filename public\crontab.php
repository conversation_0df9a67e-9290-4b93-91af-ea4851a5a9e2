<?php 

$urls = [
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryHandleFirstUpcomingPlan",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestRecored",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestRecheck",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestFinishCutOffTime",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryLedgerAccounting",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoverySummaryComplete",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestOpenAndMinusFreeze",
	//"http://nextpay-web.local.com/api-request-debt/public/DebtRecoverySummaryCheckLedgerExsist",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestCanhBaoTrichNgay",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoverySummaryCheckRefund",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryLedgerAccountingSummary",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryWaitProcessHandle",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoverySummaryHandleMailOverdue",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryContractEventCreateSend",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoverySummaryHandleMailOverCycle?mode=all",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryJobNotifyDebtNow",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryContractPlanCreateRequest",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestAdjustmentProcess",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryContractCreateRequestViaWallet",
	// "http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestCheckPayment",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryPartnerCheck",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryContractGuideCreatePlan",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryRequestSendPayment",
	"http://nextpay-web.local.com/api-request-debt/public/DebtRecoveryGuideAutoApproved"
];

$multiHandle = curl_multi_init();
$curlHandles = [];

// Khởi tạo tất cả các cURL handle
foreach ($urls as $url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_multi_add_handle($multiHandle, $ch);
    $curlHandles[] = $ch;
}

// Thực thi tất cả bất đồng bộ
$running = null;
do {
    curl_multi_exec($multiHandle, $running);
    curl_multi_select($multiHandle); // chờ khi có handle nào hoàn tất
} while ($running > 0);

// Lấy kết quả và đóng handle
foreach ($curlHandles as $ch) {
    $response = curl_multi_getcontent($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    echo "HTTP {$httpCode}: {$response}\n";

    curl_multi_remove_handle($multiHandle, $ch);
    curl_close($ch);
}

curl_multi_close($multiHandle);