<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\AttachMcRequestPaymentGuideAction;

use App\Lib\Helper;
use App\Lib\NextlendCore;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentGuideMerchant;

class AttachMcRequestPaymentGuideAction
{
	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}

	public function run()
	{
		return RequestPaymentGuide::query()
			->where('status', CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NEW)
			->doesntHave('requestPaymentGuideMerchant')
			->chunkById(30, function ($requestPaymentGuides) {
				foreach ($requestPaymentGuides as $requestPaymentGuide) {
					$paymentGuide = [];

					$detailContract = $this->nextlendCore->callRequest([
						// 'contract_code' => $requestPaymentGuide->contract_code,
						'contract_code' => 'MPOS-*************-L6',
					], 'ContractV3_getDataInfoByContract', 'get');

					$decryptData = $this->nextlendCore->decryptData();

					foreach ($decryptData['contract_account_payment_channel'] as $item) {
						$paymentAccountId = $item['account_number'];

						if ($item['payment_method_code'] == 'MPOS') {
							$accoutnData = json_decode($item['account_data'], true);
							$paymentAccountId = $accoutnData['partner_merchant_code'];
						}

						$paymentGuide[] = [
							'payment_method_code' => $item['payment_method_code'],
							'payment_channel_code' => $item['payment_method_code'],
							'payment_account_id' => $paymentAccountId,
							'other_data' => [],
						];
					}


					$dataUpdated = [
						'profile_id' => $decryptData['contract']['profile_id'],
						'payment_guide' => json_encode($paymentGuide),
						'time_updated' => now()->timestamp,
						'updated_by' => Helper::getSystemUser()
					];

					$r =  $requestPaymentGuide->update($dataUpdated);
					if (!$r) {
						throw new BusinessException('Lỗi cập nhật thông tin yêu cầu thu hộ');
					}

					$merchantData = [
						'merchant_id' => $decryptData['contract']['merchant_id'],
						'request_payment_guide_id' => $requestPaymentGuide->id,
						'merchant_name' => $decryptData['merchant']['fullname'],
						'representation_name' => $decryptData['merchant']['business_representative'],
						'email' => $decryptData['merchant']['email'],
						'mobile' =>  $decryptData['merchant']['mobile'],
						'address' => sprintf('%s - %s - %s', $decryptData['merchant']['address'], $decryptData['merchant']['address1'], $decryptData['merchant']['address2']),
						'passport' => $decryptData['merchant']['passport'],
						'other_data' => json_encode([]),
						'time_created' => now()->timestamp,
						'time_updated' => now()->timestamp,
					];

					$requestPaymentGuideMerchant = RequestPaymentGuideMerchant::query()->forceCreate($merchantData);
					if (!$requestPaymentGuideMerchant) {
						throw new BusinessException('Lỗi tạo merchant yêu cầu thu hộ');
					}
				}
			});
	}
} // End class
