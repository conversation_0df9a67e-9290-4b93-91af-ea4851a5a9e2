<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\ReportRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Interfaces\ReportRequestPaymentInterface;

class ReportRequestPaymentAction
{
	public function run()
	{
		$requestPayments = RequestPayment::query()
			->with('requestPaymentGuide:partner_request_id,partner_code')
			->where('id', '>', 0)
			->limit(10)
			->get();

		if ($requestPayments->isEmpty()) {
			return 'EMPTY';
		}

		$requestPayments->each(function (RequestPayment $requestPayment) {
			$reportResult = app(ReportRequestPaymentInterface::class, [
				'partner_code' => $requestPayment->requestPaymentGuide->partner_code,
			])->report($requestPayment);

			
		});
	}
}
