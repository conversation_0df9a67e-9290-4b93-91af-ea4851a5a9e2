<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentReceipt\MakeRequestPaymentFromReceiptAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentReceipt;
use App\Modules\CollectDebt\DTOs\RequestPayment\CreateRequestPaymentDTO;
use App\Modules\CollectDebt\DTOs\RequestPayment\CreateRequestPaymentFromReceiptDTO;
use App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\SubAction\HandleVANotiSubAction;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\ApproveRequestPaymentGuideAction\SubAction\CreateRequestPaymentSubAction;
use App\Modules\CollectDebt\Actions\RequestPaymentReceipt\MakeRequestPaymentFromReceiptAction\SubAction\CreateRequestFromReceiptSubAction;

class MakeRequestPaymentFromReceiptAction
{
	private array $__processedIds = [];
	private array $__excludedIds = [];
	private const MAX_ITERATIONS = 40;

	public function run(): array
	{
		for ($i = 1; $i < self::MAX_ITERATIONS; $i++) {
			try {
				$result = $this->processReceipt();
				if ($result === 'EMPTY') {
					break;
				}

				if (optional($result)->id) {
					$this->__processedIds[] = $result->id;
				}
			} catch (\Throwable $th) {
				throw $th;
			}
		}

		return $this->__processedIds;
	}

	private function processReceipt()
	{
		$receipt = $this->__findNextReceipt();

		if (!$receipt) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $receipt->id;

		$wasUpdateProcessing = $this->__updateReceiptToProcessing($receipt);

		if (!$wasUpdateProcessing) {
			return;
		}

		DB::beginTransaction();
		try {
			// Đã thông báo đối tác thì khoản này là khoản thu thừa
			if ($receipt->requestPaymentGuide->status == CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER) {
				$wasUpdateLateNoti = $receipt->update([
					'time_updated' => now()->timestamp,
					'status' => CollectDebtEnum::RP_RECEIPT_LATE_NOTI,
					'description' => 'Tiền về từ VA đã bị muộn khi yêu cầu thu hộ đã đóng'
				]);

				if (!$wasUpdateLateNoti) {
					throw new BusinessException('Lỗi không cập nhật được trạng thái tiền về là đã muộn');
				}

				DB::commit();
			}

			$requestPayment = $this->__createRequestPayment($receipt);
			
			if (!$requestPayment) {
				throw new BusinessException('Lỗi không cập nhật được lệnh trích');
			}

			$wasDoneReceipt = $this->__updateReceiptToCompleted($receipt, $requestPayment);
			
			if ($wasDoneReceipt !== true) {
				throw new BusinessException('Lỗi không cập nhật được trạng thái tiền về là đã xử lý');
			}

			// Cộng tịnh tiến số tiền thu được vào RequestPaymentGuide
			$requestPaymentGuide = $this->__updatePaymentGuideAmount($receipt);
			
			if (!$requestPaymentGuide) {
				throw new BusinessException('Lỗi không tịnh tiến số tiền thành công của yêu cầu thu hộ');
			}

			// Hủy lệnh trích MPOS của yc thu hộ nếu khả thi
			app(HandleVANotiSubAction::class)->createCancelDebitIfExist($requestPayment);

			DB::commit();
			return $receipt;
		} catch (\Throwable $th) {
			Log::error("Error logic tien ve", ['line' => $th->getLine(), 'message' => $th->getMessage()]);
			
			DB::rollBack();

			RequestPaymentReceipt::query()->where([
				'id' => $receipt->id,
				'status' => CollectDebtEnum::RP_RECEIPT_PROCESSING,
			])->update([
				'status' => CollectDebtEnum::RP_RECEIPT_APPROVED,
				'time_updated' => now()->timestamp,
			]);
		

			throw $th;
		}
	}

	private function __findNextReceipt(): ?RequestPaymentReceipt
	{
		$query = RequestPaymentReceipt::query()
			->with(['requestPaymentGuide'])
			->where('status', CollectDebtEnum::RP_RECEIPT_APPROVED);

		if (!empty($this->__excludedIds)) {
			$query = $query->whereNotIn('id', $this->__excludedIds);
		}

		return $query->orderByRaw(request('orderByRaw', 'id asc'))->first();
	}

	private function __updateReceiptToProcessing(RequestPaymentReceipt $receipt)
	{
		$wasUpdated = RequestPaymentReceipt::query()
			->where([
				'id' => $receipt->id,
				'status' => CollectDebtEnum::RP_RECEIPT_APPROVED,
			])
			->update([
				'status' => CollectDebtEnum::RP_RECEIPT_PROCESSING,
				'time_updated' => now()->timestamp,
			]);

		if (!$wasUpdated) {
			throw new BusinessException('Khong the xu ly tien ve thanh PROCESSING', 5000);
		}

		return $wasUpdated;
	}

	private function __createRequestPayment(RequestPaymentReceipt $receipt): RequestPayment
	{
		$currentTime = now()->timestamp;

		switch ($receipt->payment_method_code) {
			case 'MPOS':
				$dto = $this->buildDTOForMPOS($receipt, $currentTime);
				break;

			case 'IB_OFF':
				$dto = $this->buildDTOForIBOff($receipt, $currentTime);
				break;

			case 'VIRTUALACCOUNT':
				$dto = $this->buildDTOForVirtualAccount($receipt, $currentTime);
				break;

			default:
				throw new BusinessException('Phương thức thu nợ không hợp lệ', 5000);
		}

		return app(CreateRequestFromReceiptSubAction::class)->run($dto);
	}



	private function buildDTOForMPOS(RequestPaymentReceipt $receipt, int $currentTime): CreateRequestPaymentFromReceiptDTO
	{
		return CreateRequestPaymentFromReceiptDTO::fromArray([
			'profile_id' => $receipt->requestPaymentGuide->profile_id,
			'contract_code' => $receipt->contract_code,
			'payment_method_code' => $receipt->payment_method_code,
			'payment_channel_code' => $receipt->payment_channel_code,
			'payment_account_id' => $receipt->payment_account_id,
			'partner_request_id' => $receipt->partner_request_id,
			'partner_transaction_id' => $receipt->partner_transaction_id,
			'status' => CollectDebtEnum::RQ_STT_COMPLETED,
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_RECEIVED,
			'partner_request_id' => $receipt->partner_request_id,
			'partner_transaction_id' => $receipt->partner_transaction_id,
			'amount_receiver' => $receipt->amount,
			'time_receivered' => $currentTime,
			'receivered_by' => $receipt->created_by,
			'time_completed' => $currentTime,
			'completed_by' => $receipt->created_by,
			'description' => $receipt->description, 
		]);
	}

	private function buildDTOForIBOff(RequestPaymentReceipt $receipt, int $currentTime): CreateRequestPaymentFromReceiptDTO
	{
		return CreateRequestPaymentFromReceiptDTO::fromArray([
			'type' => $receipt->requestPaymentGuide->type,
			'profile_id' => $receipt->requestPaymentGuide->profile_id,
			'contract_code' => $receipt->contract_code,
			'payment_method_code' => $receipt->payment_method_code,
			'payment_channel_code' => $receipt->payment_channel_code,
			'payment_account_id' => $receipt->payment_account_id,
			'partner_request_id' => $receipt->partner_request_id,
			'partner_transaction_id' => $receipt->partner_transaction_id,
			'amount_request' => (float) $receipt->amount,
			'amount_payment' => (float) $receipt->amount,
			'amount_receiver' => $receipt->amount,
			'fee' => 0.0,
			'time_begin' => $currentTime,
			'time_expired' => now()->endOfDay()->timestamp,
			'is_payment' => 1,
			'status' => CollectDebtEnum::RQ_STT_COMPLETED,
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_RECEIVED,
			'description' => $receipt->description,
			'create_from' => CollectDebtEnum::REQUEST_PAYMENT_AUTO,
			'created_by' => $receipt->created_by,
			'updated_by' => $receipt->created_by,
			'approved_by' => $receipt->created_by,
			'completed_by' => $receipt->created_by,
			'completed_recheck_by' => $receipt->created_by,
			'time_created' => $currentTime,
			'time_updated' => $currentTime,
			'time_approved' => $currentTime,
			'time_completed' => $currentTime,
			'time_completed_recheck' => $currentTime,
			'time_receivered' => $currentTime,
			'receivered_by' => $receipt->created_by,
			'request_payment_guide_id' => $receipt->requestPaymentGuide->id,
		]);
	}

	private function buildDTOForVirtualAccount(RequestPaymentReceipt $receipt, int $currentTime): CreateRequestPaymentFromReceiptDTO
	{
		return CreateRequestPaymentFromReceiptDTO::fromArray([
			'profile_id' => $receipt->requestPaymentGuide->profile_id,
			'contract_code' => $receipt->contract_code,
			'payment_method_code' => $receipt->payment_method_code,
			'payment_channel_code' => $receipt->payment_channel_code,
			'payment_account_id' => $receipt->payment_account_id,
			'partner_request_id' => $receipt->partner_request_id,
			'partner_transaction_id' => $receipt->partner_transaction_id,
			'status' => CollectDebtEnum::RQ_STT_COMPLETED,
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_RECEIVED,
			'partner_request_id' => $receipt->partner_request_id,
			'partner_transaction_id' => $receipt->partner_transaction_id,
			'amount_receiver' => $receipt->amount,
			'time_receivered' => $currentTime,
			'receivered_by' => $receipt->created_by,
			'time_completed' => $currentTime,
			'completed_by' => $receipt->created_by,
			'description' => $receipt->description,
		]);
	}

	private function __updateReceiptToCompleted(RequestPaymentReceipt $receipt, $requestPayment): bool
	{
		$wasUpdated = RequestPaymentReceipt::query()
			->where('id', $receipt->id)
			->where('status', CollectDebtEnum::RP_RECEIPT_PROCESSING)
			->update([
				'partner_request_id' => $requestPayment->partner_request_id,
				'status' => CollectDebtEnum::RP_RECEIPT_COMPLETED,
				'time_created_request' => now()->timestamp,
				'created_request_by' => $receipt->created_by,
				'time_updated' => now()->timestamp,
			]);

		if (!$wasUpdated) {
			throw new BusinessException('Không thể cập nhật mã trích nợ thành công', 5000);
		}

		return true;
	}

	private function __updatePaymentGuideAmount(RequestPaymentReceipt $receipt): RequestPaymentGuide
	{
		$requestPaymentGuide = RequestPaymentGuide::query()->find($receipt->requestPaymentGuide->id);
		
		if (!$requestPaymentGuide) {
			throw new BusinessException('Không tìm thấy yêu cầu thu hồ', 5000);
		}

		$requestPaymentGuide->amount_debited += $receipt->amount;
		$requestPaymentGuide->time_updated = now()->timestamp;

		if ($requestPaymentGuide->amount_debited >= $requestPaymentGuide->amount) {
			if ($requestPaymentGuide->status_debited != CollectDebtEnum::RPG_CLOSED) {
				$requestPaymentGuide->status_debited = CollectDebtEnum::RPG_READY_TO_CLOSE;
			}
		}else {
			// Số tiền vẫn chưa đủ thì tiếp tục tạo lệnh trích vét
			$requestPaymentGuide->status = CollectDebtEnum::REQUEST_PAYMENT_GUIDE_READY_TRICH_VET;
		}
		
		$wasUpdated = $requestPaymentGuide->save();

		if (!$wasUpdated) {
			throw new BusinessException('Không thể cập nhật số tiền thu nợ thành công', 5000);
		}

		return $requestPaymentGuide;
	}

	
	private function __createSweepDebitCmd(RequestPaymentGuide $requestPaymentGuide, RequestPayment $requestPayment)
	{
		$sweepAmount = $requestPaymentGuide->getSweepAmount();
		
		/**
		 * Lấy ra các lệnh trích MPOS chưa có tiền về
		 * 		Nếu có kết quả: Thì không tạo lệnh vét
		 * 		Nếu chưa có thì tạo lệnh vét ngay
		 */

		$otherRequestPayment = RequestPayment::query()
			->where('request_payment_guide_id', $requestPaymentGuide->id)
			->where('payment_method_code', 'MPOS')
			->doesntHave('requestPaymentReceipt')
			->first();
		
		if ($otherRequestPayment) {
			return;
		}

		// Tạo lệnh trích vét
		$dto = new CreateRequestPaymentDTO(
			$requestPaymentGuide->type,
			$requestPaymentGuide->profile_id,
			$requestPaymentGuide->contract_code,
			'MPOS',
			'MPOS',
			$requestPaymentGuide->getMposMcId(),
			'', // partner_request_id
			$sweepAmount,
			$sweepAmount,
			now()->timestamp,
			$requestPaymentGuide->time_expired,
			$requestPayment->created_by,
			now()->timestamp,
			'',
			$requestPaymentGuide->id,
			$requestPaymentGuide->partner_code
		);

		return app(CreateRequestPaymentSubAction::class)->run($dto);
	}
} // End class