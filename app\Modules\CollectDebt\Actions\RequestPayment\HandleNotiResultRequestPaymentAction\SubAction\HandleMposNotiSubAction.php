<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\HandleNotiResultRequestPaymentAction\SubAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;
use App\Modules\CollectDebt\Actions\RequestPayment\DetailRequestPaymentAction\DetailRequestPaymentAction;

class HandleMposNotiSubAction
{
	public CheckRequestPaymentAction $checkRequestPaymentAction;

	public function __construct(CheckRequestPaymentAction $checkRequestPaymentAction)
	{
		$this->checkRequestPaymentAction = $checkRequestPaymentAction;
	}

	public function run(RequestPaymentLog $requestPaymentLog, $responseJson)
	{
		$partnerRequestId = $responseJson['data']['lendingReferenceId'] ?? '';

		$requestPayment = app(DetailRequestPaymentAction::class)->run([
			'partner_request_id' => $partnerRequestId,
		]);

		if (!$requestPayment) {
			return $this->markAsDone($requestPaymentLog);
		}

		// Lệnh trích về trạng thái cuối thì không xử lý nữa
		if ($requestPayment->isFinalStatusPayment()) {
			return $this->markAsDone($requestPaymentLog);
		}

		// Gọi check lệnh trích
		$checkResult = $this->checkRequestPaymentAction->executeCheck($requestPayment);
		$this->checkRequestPaymentAction->handleCheckResult($requestPayment, $checkResult);

		// Đánh dấu lệnh trích đã xử lý
		return $this->markAsDone($requestPaymentLog);
	}

	public function markAsDone(RequestPaymentLog $requestPaymentLog): RequestPaymentLog
	{
		$requestPaymentLog->status = CollectDebtEnum::RQP_LOG_STT_COMPLETED;
		$requestPaymentLog->updated_at = now();
		$requestPaymentLog->save();

	
		return $requestPaymentLog;
	}
} // End class
