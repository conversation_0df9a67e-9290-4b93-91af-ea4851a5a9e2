<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\CloseRequestPaymentGuideAction;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class CloseRequestPaymentGuideAction
{
	private array $__processedIds = [];

	public function run()
	{
		$requestPaymentGuides = RequestPaymentGuide::query()
			->with('requestPayments:id,contract_code,status_payment,request_payment_guide_id')
			->where('status_debited', CollectDebtEnum::RPG_READY_TO_CLOSE)
			->orWhereRaw("(status_debited = ? AND time_expired < ?)", [CollectDebtEnum::RPG_NOT_READY_CLOSE, now()->timestamp])
			->select(['*'])
			->orderBy('status_debited', 'DESC')
			->limit(30)
			->get();

		if ($requestPaymentGuides->isEmpty()) {
			return 'EMPTY';
		}

		$requestPaymentGuides->each(function ($requestPaymentGuide) {
			// Kiểm tra các lệnh trích của guide hiện tại đã về trạng thái cuối chưa, chưa về thì close hết đi
			$isAllRequestPaymentEnd = $requestPaymentGuide->requestPayments->every(function ($requestPayment) {
				return $requestPayment->status_payment == CollectDebtEnum::RP_STT_PAYMENT_RECEIVED;
			});

			
			if (!$isAllRequestPaymentEnd) {
				return;
			}

			// giả lập close hết về trạng thái đóng
			$requestPaymentGuide->status_debited = CollectDebtEnum::RPG_CLOSED;
			$requestPaymentGuide->time_notice_partner = now()->timestamp;
			$requestPaymentGuide->status = CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER;
			$requestPaymentGuide->save();

			// Gọi api để đóng yc trích
			$this->__processedIds[] = $requestPaymentGuide->id;
			return $requestPaymentGuide;
		});

		return $this->__processedIds;
	}
} // End class