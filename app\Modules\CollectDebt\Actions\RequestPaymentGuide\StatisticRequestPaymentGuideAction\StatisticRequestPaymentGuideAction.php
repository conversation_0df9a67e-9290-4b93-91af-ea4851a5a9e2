<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\StatisticRequestPaymentGuideAction;

use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\DTOs\RequestPaymentGuide\GetAllRequestPaymentGuideDTO;

class StatisticRequestPaymentGuideAction
{
	public function run(GetAllRequestPaymentGuideDTO $dto): ?RequestPaymentGuide
	{
		$statistic = RequestPaymentGuide::query()
			->where('partner_code', $dto->partner_code)
			->selectRaw("SUM(amount) as total_amount_request, SUM(amount_debited) as total_amount_success")
			->groupBy('partner_code')
			->first();

		return $statistic;
	}
}
