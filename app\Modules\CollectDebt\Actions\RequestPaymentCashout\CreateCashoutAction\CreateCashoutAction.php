<?php
namespace App\Modules\CollectDebt\Actions\RequestPaymentCashout\CreateCashoutAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Model\RequestPaymentCashout;

class CreateCashoutAction
{
	private const MAX_ITERATIONS = 40;
	private const EMPTY_RESULT = 'EMPTY';

	private array $processedIds = [];
	private array $excludedIds = [];
	private int $currentTimestamp;

	public function __construct()
	{
		$this->currentTimestamp = now()->timestamp;
	}

	/**
	 * Xử lý tạo cashout cho các RequestPaymentGuide
	 *
	 * @return array Danh sách ID đã xử lý
	 */
	public function run(): array
	{
		for ($i = 1; $i < self::MAX_ITERATIONS; $i++) {
			$requestPaymentGuide = $this->processCreateCashout();

			if ($requestPaymentGuide === self::EMPTY_RESULT) {
				$this->processedIds[] = 'EMPTY';
				break;
			}

			$this->processedIds[] = $requestPaymentGuide->id;
		}

		return $this->processedIds;
	}

	/**
	 * Xử lý tạo một cashout record
	 *
	 * @return RequestPaymentGuide|string
	 * @throws BusinessException
	 */
	private function processCreateCashout()
	{
		$requestPaymentGuide = $this->findEligibleRequestPaymentGuide();

		if (!$requestPaymentGuide) {
			return self::EMPTY_RESULT;
		}

		$this->excludedIds[] = $requestPaymentGuide->id;

		// Cập nhật trạng thái thành CREATING trước khi tạo cashout
		$this->updateStatusToCreating($requestPaymentGuide);

		// Tạo cashout record trong transaction
		return $this->createCashoutRecord($requestPaymentGuide);
	}

	/**
	 * Tìm RequestPaymentGuide đủ điều kiện để tạo cashout
	 *
	 * @return RequestPaymentGuide|null
	 */
	private function findEligibleRequestPaymentGuide(): ?RequestPaymentGuide
	{
		$query = RequestPaymentGuide::query()
			->where('status_debited', CollectDebtEnum::RPG_CLOSED)
			->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE);

		if (!empty($this->excludedIds)) {
			$query->whereNotIn('id', $this->excludedIds);
		}

		return $query->orderByRaw(request('orderByRaw', 'id asc'))->first();
	}

	/**
	 * Cập nhật trạng thái cashout thành CREATING
	 *
	 * @param RequestPaymentGuide $requestPaymentGuide
	 * @throws BusinessException
	 */
	private function updateStatusToCreating(RequestPaymentGuide $requestPaymentGuide): void
	{
		$wasUpdated = RequestPaymentGuide::query()
			->where('id', $requestPaymentGuide->id)
			->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE)
			->update([
				'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_CREATING,
				'time_updated' => $this->currentTimestamp,
			]);

		if (!$wasUpdated) {
			throw new BusinessException('Lỗi không thể cập nhật trạng thái thành đang tạo yêu cầu cashout');
		}
	}

	/**
	 * Tạo cashout record trong database transaction
	 *
	 * @param RequestPaymentGuide $requestPaymentGuide
	 * @return RequestPaymentGuide
	 * @throws BusinessException
	 */
	private function createCashoutRecord(RequestPaymentGuide $requestPaymentGuide): RequestPaymentGuide
	{
		DB::beginTransaction();

		try {
			// Số tiền thu được >0 thì mới cần tạo casout
			if ($requestPaymentGuide->amount_debited > 0) {
				$cashout = RequestPaymentCashout::query()->forceCreate([
					'request_payment_guide_id' => $requestPaymentGuide->id,
					'partner_request_id' => $requestPaymentGuide->partner_request_id,
					'amount' => $requestPaymentGuide->amount_debited,
					'time_created' => $this->currentTimestamp,
					'time_updated' => $this->currentTimestamp
				]);

				if (!$cashout) {
					throw new BusinessException('Lỗi không thể tạo cashout');
				}
			}

			// Cập nhật trạng thái thành CREATED
			$this->updateStatusToCreated($requestPaymentGuide);

			DB::commit();

			return $requestPaymentGuide;

		} catch (\Throwable $th) {
			DB::rollBack();
			$this->handleCashoutCreationError($requestPaymentGuide, $th);
			throw $th;
		}
	}

	/**
	 * Cập nhật trạng thái cashout thành CREATED
	 *
	 * @param RequestPaymentGuide $requestPaymentGuide
	 * @throws BusinessException
	 */
	private function updateStatusToCreated(RequestPaymentGuide $requestPaymentGuide): void
	{
		$statusCashout = CollectDebtEnum::RPG_STT_CASHOUT_CREATED;
		
		if ($requestPaymentGuide->amount_debited == 0) {
			$statusCashout = CollectDebtEnum::RPG_STT_CASHOUT_EMPTY_NOTI;
		}

		$wasUpdated = RequestPaymentGuide::query()
			->where('id', $requestPaymentGuide->id)
			->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_CREATING)
			->update([
				'status_cashout' => $statusCashout,
				'time_updated' => $this->currentTimestamp,
			]);

		if (!$wasUpdated) {
			throw new BusinessException('Lỗi không thể cập nhật trạng thái yêu cầu cashout thành đã tạo');
		}
	}

	/**
	 * Xử lý lỗi khi tạo cashout - rollback trạng thái về UNCREATE
	 *
	 * @param RequestPaymentGuide $requestPaymentGuide
	 * @param \Throwable $th
	 */
	private function handleCashoutCreationError(RequestPaymentGuide $requestPaymentGuide, \Throwable $th): void
	{
		try {
			RequestPaymentGuide::query()
				->where('id', $requestPaymentGuide->id)
				->where('status_cashout', CollectDebtEnum::RPG_STT_CASHOUT_CREATING)
				->update([
					'status_cashout' => CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE,
					'time_updated' => $this->currentTimestamp
				]);
		} catch (\Throwable $rollbackError) {
			Log::error("Lỗi khi rollback trạng thái cashout", [
				'request_payment_guide_id' => $requestPaymentGuide->id,
				'rollback_error' => $rollbackError->getMessage(),
				'original_error' => $th->getMessage()
			]);
		}

		Log::error("Lỗi tạo cashout", [
			'request_payment_guide_id' => $requestPaymentGuide->id,
			'line' => $th->getLine(),
			'message' => $th->getMessage(),
			'file' => $th->getFile()
		]);
	}
} // End class