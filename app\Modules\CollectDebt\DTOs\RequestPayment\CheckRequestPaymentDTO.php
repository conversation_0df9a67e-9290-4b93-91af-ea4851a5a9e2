<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Modules\CollectDebt\Model\RequestPayment;

class CheckRequestPaymentDTO
{
	public $nextlend_request_id;
	public $partner_code;
	public $merchantId;
	public $lendingRequestId;
	public $debitAmount;
	public $requestTime;
	public $lendingId;
	public $loanOriginalAmount;
	public $deductionPerDayAmount;
	public $loanBalance;
	public $_prefix;

	public function __construct(
		int $nextlendRequestId,
		string $merchantId,
		string $lendingRequestId,
		float $debitAmount,
		string $requestTime,
		string $lendingId,
		float $loanOriginalAmount,
		float $deductionPerDayAmount,
		float $loanBalance,
		string $prefix = ''
	) {
		$this->nextlend_request_id = $nextlendRequestId;
		$this->partner_code = 'MPOS';
		$this->merchantId = $merchantId;
		$this->lendingRequestId = $lendingRequestId;
		$this->debitAmount = $debitAmount;
		$this->requestTime = $requestTime;
		$this->lendingId = $lendingId;
		$this->loanOriginalAmount = $loanOriginalAmount;
		$this->deductionPerDayAmount = $deductionPerDayAmount;
		$this->loanBalance = $loanBalance;
		$this->_prefix = $prefix;
	}

	/**
	 * Tạo DTO từ RequestPayment model để kiểm tra tình trạng
	 */
	public static function fromRequestPayment(RequestPayment $requestPayment, string $prefix = ''): self
	{
		return new self(
			$requestPayment->id,
			$requestPayment->payment_account_id,
			$requestPayment->partner_request_id,
			$requestPayment->amount_request + $requestPayment->fee,
			date('YmdHis'),
			$requestPayment->contract_code,
			$requestPayment->amount_request,
			$requestPayment->amount_request,
			$requestPayment->amount_request,
			$prefix
		);
	}

	/**
	 * Tạo DTO từ array
	 */
	public static function fromArray(array $data): self
	{
		return new self(
			$data['nextlend_request_id'],
			$data['merchantId'],
			$data['lendingRequestId'],
			$data['debitAmount'],
			$data['requestTime'] ?? date('YmdHis'),
			$data['lendingId'],
			$data['loanOriginalAmount'],
			$data['deductionPerDayAmount'],
			$data['loanBalance'],
			$data['_prefix'] ?? ''
		);
	}

	/**
	 * Chuyển đổi DTO thành array
	 */
	public function toArray(): array
	{
		return get_object_vars($this);
	}
}
