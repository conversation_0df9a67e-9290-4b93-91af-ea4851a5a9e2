<?php

namespace App\Modules\CollectDebt\Rules;

use Illuminate\Contracts\Validation\Rule;

class MatchingPaymentAccountIdRule implements Rule
{
	public function __construct() {}

	public function passes($attribute, $value)
	{
		$requestData = request()->json('data');
		/**
				 * array:1 [
			0 => array:14 [
				"contract_code" => "BIDV-*************-L17"
				"type" => 1
				"payment_guide" => array:2 [
					0 => array:4 [
						"payment_method_code" => "MPOS"
						"payment_channel_code" => "MPOS"
						"payment_account_id" => "*********"
						"other_data" => []
					]
					1 => array:4 [
						"payment_method_code" => "VIRTUALACCOUNT"
						"payment_channel_code" => "VIRTUALACCOUNT"
						"payment_account_id" => "*********999"
						"other_data" => []
					]
				]
				"merchant" => array:7 [
					"merchant_id" => "*********"
					"merchant_name" => "CTCP BARISTA MARTS"
					"representation_name" => "MPOS SUPER ADMIN Tên NDD"
					"email" => "<EMAIL>"
					"mobile" => "***********"
					"address" => "220 Hai bà trưng, Hà nội"
					"passport" => "************"
				]
				"partner_code" => "BIDV"
				"amount" => ********
				"partner_request_id" => "NLTHBIDV12345679987"
				"created_by" => "quantri"
				"requested_by" => "quantri"
				"requested_at" => "2025-07-09 22:00:00"
				"expired_at" => null
				"profile_id" => *********
				"is_approved" => true
				"is_required_notify" => false
			]
]
		 */
		foreach ($requestData as $item) {
			$merchantId = $item['merchant']['merchant_id'];

			$collect = collect($item['payment_guide']);

			$hasMposChannel = $collect->where('payment_method_code', 'MPOS')->count() > 0;

			if ($hasMposChannel) {
				$mposPaymentAccountId = $collect->first(function ($pm) use ($merchantId) {
					return $pm['payment_account_id'] == $merchantId;
				});

				if (empty($mposPaymentAccountId)) {
					return false;
				}
			}
			
		}
		
		return true;
	}

	/**
	 * Get the validation error message.
	 *
	 * @return string
	 */
	public function message()
	{
		return 'PaymentAccountId phải bằng với MerchantId nếu kênh thu là MPOS!!!';
	}
}
