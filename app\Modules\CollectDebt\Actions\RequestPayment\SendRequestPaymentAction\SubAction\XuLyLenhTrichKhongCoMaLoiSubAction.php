<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;

class XuLyLenhTrichKhongCoMaLoiSubAction
{
  /**
   * Bối cảnh: gửi lệnh trích những không có mã lỗi (errorCode) -> không có cơ sở để lấy đc partner transaction
   * B1: Đ<PERSON>h dấu là Gửi lỗi (và sẽ gọi gửi lại vào các phút: 0, 20, 40 của từng giờ)
   * B2: Nên có job để gọi lại theo các thông số ở B1
   */
  public function run(RequestPayment $requestPayment): RequestPayment
  {
		$wasUpdateSendError = RequestPayment::query()->where([
			'id' => $requestPayment->id,
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDING
		])->update([
			'status_payment' => CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR,
			'time_sended' => now()->timestamp,
			'time_updated' => now()->timestamp
		]);

		if (!$wasUpdateSendError) {
			throw new BusinessException('Loi khong update trang thai gui lenh thanh that bai');
		}

		return $requestPayment;
  }
} // End class