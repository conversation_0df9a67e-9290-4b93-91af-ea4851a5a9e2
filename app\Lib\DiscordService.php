<?php

namespace App\Lib;

use Illuminate\Support\Facades\Http;

class DiscordService
{
	public function sendMessage(array $message = [], $channel = 'tao_yc_thu_ho')
	{
		$webhookUrl = config('services.discord.' . $channel);
		return $this->push($webhookUrl, $message);
	}

	public function push(string $webhookUrl, array $message)
	{
		$message = [
			'time' => now()->toDateTimeString(), 
			'env' => config('app.env')
		] + $message;

		return Http::timeout(3)
			->retry(1, 500)
			->withHeaders([
				'Content-Type' => 'application/json',
			])
			->post($webhookUrl, [
				'content' => json_encode($message, JSON_UNESCAPED_UNICODE)
			]);
	}
} // End class
