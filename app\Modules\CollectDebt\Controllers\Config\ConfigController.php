<?php

namespace App\Modules\CollectDebt\Controllers\Config;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Controllers\Controller;

class ConfigController extends Controller
{
	public function GetConfig()
	{
		$config = $this->GetConfigAsArray();
		return $this->successResponse($config);
	}

	public function GetConfigAsArray()
	{
		$config = [
			'request_payment_guide' => [
				'status' => [
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NEW, 'name' => 'Chờ duyệt', 'badge_class' => 'badge badge-secondary'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED, 'name' => 'Đã duyệt', 'badge_class' => 'badge badge-info'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_PROCESSING_COMMAND, 'name' => '<PERSON>ang xử lý tạo lệnh trích', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_MADE_COMMAND, 'name' => 'Đang thu hồi nợ', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_DENIED, 'name' => 'Đã từ chối yêu cầu', 'badge_class' => 'badge badge-danger'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NOTICE_PARTNER, 'name' => 'Đã đóng yêu cầu', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_READY_TRICH_VET, 'name' => 'Chờ tạo lệnh trích vét', 'badge_class' => 'badge badge-warning'],
				],
				'type' => [
					['id' => 1, 'name' => 'Thấu chi',],
					['id' => 2, 'name' => 'Khoản ứng',],
				],
				'status_debited' => [
					['id' => CollectDebtEnum::RPG_NOT_READY_CLOSE, 'name' => 'Đang thu hộ', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::RPG_READY_TO_CLOSE, 'name' => 'Đủ điều kiện đóng yêu cầu thu hộ', 'badge_class' => 'badge badge-primary'],
					['id' => CollectDebtEnum::RPG_CLOSED, 'name' => 'Đã đóng yêu cầu thu hộ', 'badge_class' => 'badge badge-success'],
				],
				'status_cashout' => [
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_UNCREATE, 'name' => 'Chưa có thông tin', 'badge_class' => 'badge badge-secondary'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_CREATING, 'name' => 'Đang tạo lệnh báo có', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_CREATED, 'name' => 'Chờ đối tác xử lý', 'badge_class' => 'badge badge-info'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_PUSHED, 'name' => 'Đối tác đang xử lý báo có', 'badge_class' => 'badge badge-primary'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_SUCCESS_PAYOUT, 'name' => 'Báo có thành công', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_FAILED_PAYOUT, 'name' => 'Báo có thất bại', 'badge_class' => 'badge badge-danger'],
					['id' => CollectDebtEnum::RPG_STT_CASHOUT_EMPTY_NOTI, 'name' => 'Không báo - không thu được', 'badge_class' => 'badge badge-dark'],
				]
			],

			'request_payment' => [
				'status_payment' => [
					['id' => CollectDebtEnum::RP_STT_PAYMENT_UNSEND, 'name' => 'Chưa gửi đối tác thu hồi', 'badge_class' => 'badge badge-secondary'],
					['id' => CollectDebtEnum::RP_STT_PAYMENT_SENDING, 'name' => 'Đang gửi đối tác thu hồi', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::RP_STT_PAYMENT_SENDING_ERROR, 'name' => 'Gửi lỗi', 'badge_class' => 'badge badge-danger'],
					['id' => CollectDebtEnum::RP_STT_PAYMENT_SENDED, 'name' => 'Lệnh trích đã sang đối tác', 'badge_class' => 'badge badge-primary'],
					['id' => CollectDebtEnum::RP_STT_PAYMENT_RECEIVED, 'name' => 'Đã nhận kết quả', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::RP_STT_PAYMENT_CANCELED, 'name' => 'Đã hủy', 'badge_class' => 'badge badge-danger'],
				],
				'status' => [
					['id' => CollectDebtEnum::RQ_STT_NEW, 'name' => 'Chờ duyệt', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::RQ_STT_APPROVED, 'name' => 'Đã duyệt lệnh', 'badge_class' => 'badge badge-primary'],
					['id' => CollectDebtEnum::RQ_STT_COMPLETED, 'name' => 'Hoàn thành', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::RQ_STT_CANCELED, 'name' => 'Đã từ chối', 'badge_class' => 'badge badge-danger'],
					['id' => CollectDebtEnum::RQ_STT_COMPLATED_AND_RECHECK, 'name' => 'Hoàn thành nhưng cần kiểm tra lại', 'badge_class' => 'badge badge-info'],
				]
			],

			'request_payment_receipt' => [
				'status' => [
					['id' => CollectDebtEnum::RP_RECEIPT_NEW, 'name' => 'Chờ xử lý', 'badge_class' => 'badge badge-secondary'],
					['id' => CollectDebtEnum::RP_RECEIPT_APPROVED, 'name' => 'Đã duyệt xử lý', 'badge_class' => 'badge badge-info'],
					['id' => CollectDebtEnum::RP_RECEIPT_PROCESSING, 'name' => 'Đang xử lý', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::RP_RECEIPT_COMPLETED, 'name' => 'Đã xử lý tiền về', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::RP_RECEIPT_CANCELED, 'name' => 'Đã hủy', 'badge_class' => 'badge badge-danger'],
					['id' => CollectDebtEnum::RP_RECEIPT_LATE_NOTI, 'name' => 'Báo có tiền về muộn', 'badge_class' => 'badge badge-dark'],
				]
			],

			'cashout' => [
				'status' => [
					['id' => CollectDebtEnum::CASHOUT_STT_NEW, 'name' => 'Chờ xử lý', 'badge_class' => 'badge badge-secondary'],
					['id' => CollectDebtEnum::CASHOUT_STT_PROCESSING, 'name' => 'Đang xử lý', 'badge_class' => 'badge badge-warning'],
					['id' => CollectDebtEnum::CASHOUT_STT_CREATED_CASHOUT, 'name' => 'Đã tạo yêu cầu', 'badge_class' => 'badge badge-success'],
					['id' => CollectDebtEnum::CASHOUT_STT_ERROR, 'name' => 'Có lỗi xảy ra', 'badge_class' => 'badge badge-error'],
					['id' => CollectDebtEnum::CASHOUT_STT_FAILED, 'name' => 'Chi tiền thất bại', 'badge_class' => 'badge badge-error'],
					['id' => CollectDebtEnum::CASHOUT_STT_SUCCESS, 'name' => 'Chi tiền thành công', 'badge_class' => 'badge badge-success'],
				],
			]
		];

		return $config;
	}
} // End class