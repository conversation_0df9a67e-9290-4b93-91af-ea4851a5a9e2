<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\ApproveRequestPaymentGuideAction;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\DTOs\RequestPayment\CreateRequestPaymentDTO;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\ApproveRequestPaymentGuideAction\SubAction\CreateRequestPaymentSubAction;

class ApproveRequestPaymentGuideAction
{
	private array $__processedIds = [];

	private array $__excludedIds = [];

	public function run()
	{
		for ($i = 1; $i < 40; $i++) {
			try {
				$requestPaymentGuide = $this->processGuide();
				if ($requestPaymentGuide == 'EMPTY') {
					break;
				}

				$this->__processedIds[] = $requestPaymentGuide->id;
			} catch (\Throwable $th) {
				throw $th;
			}
		}

		return $this->__processedIds;
	}

	public function processGuide()
	{
		$requestPaymentGuide = RequestPaymentGuide::query()
			->whereIn('status', [
				CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED,
				CollectDebtEnum::REQUEST_PAYMENT_GUIDE_READY_TRICH_VET
			])
			->where('status_debited', CollectDebtEnum::RPG_NOT_READY_CLOSE)
			->where('time_expired', '>', now()->timestamp)
			->whereColumn('amount_debited', '<', 'amount');

		if (!empty($this->__excludedIds)) {
			$requestPaymentGuide = $requestPaymentGuide->whereNotIn('id', $this->__excludedIds);
		}

		$requestPaymentGuide = $requestPaymentGuide->orderByRaw(request('orderByRaw', 'id asc'))->first();
		if (!$requestPaymentGuide) {
			return 'EMPTY';
		}

		$this->__excludedIds[] = $requestPaymentGuide->id;

		$wasUpdateProcessing = RequestPaymentGuide::query()->where('id', $requestPaymentGuide->id)
			->whereIn('status', [CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED, CollectDebtEnum::REQUEST_PAYMENT_GUIDE_READY_TRICH_VET])
			->update([
				'status' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_PROCESSING_COMMAND,
				'time_updated' => now()->timestamp,
				'time_approved' => now()->timestamp
			]);

		if (!$wasUpdateProcessing) {
			throw new BusinessException('Loi khong update chi dan thanh dang xu ly');
		}

		DB::beginTransaction();
		try {
			$requestPayment = false;

			foreach ($requestPaymentGuide->getPaymentGuideList() as $pg) {
				$createRequestPaymentDTO = null;
				// build dto
				if ($pg['payment_method_code'] == 'MPOS') {
					$requestMposLunching = RequestPayment::query()->where([
						'request_payment_guide_id' => $requestPaymentGuide->id,
						'payment_method_code' => $pg['payment_method_code'],
					])->whereNotIn('status', [
						CollectDebtEnum::RQ_STT_COMPLETED, 
						CollectDebtEnum::RQ_STT_CANCELED
					])->first();

					if (!$requestMposLunching) {
						$createRequestPaymentDTO = CreateRequestPaymentDTO::fromRequestPaymentGuide($requestPaymentGuide);
					}
				}
				
				if ($pg['payment_method_code'] == 'VIRTUALACCOUNT') {
					$requestVaLunching = RequestPayment::query()->firstWhere([
						'request_payment_guide_id' => $requestPaymentGuide->id,
						'payment_method_code' => $pg['payment_method_code'],
					]);

					if (!$requestVaLunching) {
						$createRequestPaymentDTO = CreateRequestPaymentDTO::fromRequestPaymentGuideVA(
							$requestPaymentGuide, 
							$pg['payment_account_id']
						);
					}
				}

				// Tạo lệnh trích
				if ($createRequestPaymentDTO) {
					$requestPayment = app(CreateRequestPaymentSubAction::class)->run($createRequestPaymentDTO);
				}
			}

			if (!$requestPayment) {
				throw new BusinessException('Lỗi không có lệnh trích nào được tạo');
			}

			// Update chỉ dẫn về trạng thái cuối
			$wasUpdateMadeCommand = $requestPaymentGuide->where([
				'id' => $requestPaymentGuide->id, 
				'status' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_PROCESSING_COMMAND
			])
			->update([
				'status' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_MADE_COMMAND,
				'time_updated' => now()->timestamp,
				'time_create_request_payment' => now()->timestamp
			]);

			if (!$wasUpdateMadeCommand) {
				throw new BusinessException('Loi khong update chi dan ve trang thai cuoi');
			}
			
			DB::commit();

			return $requestPayment;
		} catch (\Throwable $th) {
			DB::rollBack();
			$wasUpdateNew = $requestPaymentGuide->where(['id' => $requestPaymentGuide->id, 'status' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_PROCESSING_COMMAND])
				->update([
					'status' => CollectDebtEnum::REQUEST_PAYMENT_GUIDE_APPROVED,
					'time_updated' => now()->timestamp
				]);
			Log::info("Error approve guide", ['line' => $th->getLine(), 'message' => $th->getMessage()]);
			throw $th;
		}
	}
} // End class