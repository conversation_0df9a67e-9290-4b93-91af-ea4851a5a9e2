<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentReceipt;

use Illuminate\Foundation\Http\FormRequest;

class GetAllRequestPaymentReceiptRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.contract_code' => ['nullable', 'string', 'max:50'],
			'data.partner_code' => ['required', 'string'],
			'data.status' => ['nullable', 'numeric', 'integer', 'min:0'],
			'data.partner_transaction_id' => ['nullable', 'string', 'max:50'],
			'data.partner_request_id' => ['nullable', 'string', 'max:50'],
		];
	}

	public function messages()
	{
		return [];
	}
} // End class
