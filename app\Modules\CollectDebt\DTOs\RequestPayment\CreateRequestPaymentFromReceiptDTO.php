<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class CreateRequestPaymentFromReceiptDTO
{
	// Loại yêu cầu: 1-Thấu chi, 2-Hợ<PERSON> đồng vay
	public int $type;
	
	// Id profile tương ứng của hệ thống profile
	public int $profile_id;
	
	// Mã hợp đồng
	public string $contract_code;
	
	// Mã phương thức thu hồi, xem trên payment_methods
	public string $payment_method_code;
	
	// Mã kênh thu hồi được chỉ định cho yêu cầu này, tham chiếu trên payment_channel
	public string $payment_channel_code;
	
	// Tài khoản theo kênh được chỉ định sẽ thanh toán cho yêu cầu
	public string $payment_account_id;
	
	// Tên chủ tài khoản theo kênh được chỉ định
	public ?string $payment_account_holder_name;
	
	// Mã ngân hàng/ví/đối tác của tài khoản theo kênh
	public ?string $payment_account_bank_code;
	
	// Tên chỉ nhánh/địa chỉ/thông tin khác liên quan đến tài khoản theo kênh
	public ?string $payment_account_bank_branch;
	
	// Mã yêu cầu gửi sang đối tác kênh thanh toán cho yêu cầu
	public string $partner_request_id;
	
	// Mã giao dịch trên đối tác tương ứng
	public ?string $partner_transaction_id;
	
	// Mã loại tiền, mặc định VND
	public string $currency;
	
	// Số tiền mà NL yêu cầu thu hồi và gửi cho partner
	public float $amount_request;
	
	// Số tiền mà parner thu hồi thành công và bắn lại cho NL, mặc định =0
	public float $amount_payment;
	
	// Số tiền thu được sau phí: Công thức tính = amount_payment - fee, mặc định =0
	public float $amount_receiver;
	
	// Phí trích nợ <parner sẽ trả thông này về cho NL>, mặc định =0
	public float $fee;
	
	// Thời gian yêu cầu được phép gửi sang đối tác để thanh toán
	public int $time_begin;
	
	// Thời gian kết thúc cho lệnh này
	public ?int $time_expired;
	
	// Có TT không: 1-Có, 2-Không phải TT
	public int $is_payment;
	
	// Trạng thái: 1: NEW <mới tạo chưa duyệt> 2: APPROVED <đã duyệt> 3: COMPLATED<đã hoàn thành> 4: CANCELED <từ chối> 5: COMPLATED RECHECK <đã hoàn thành và cần kiểm tra>
	public int $status;
	
	// Trạng thái thanh toán 1: UNSENT <chưa gửi thanh toán> 2: SENDING <đang gửi thanh toán> 3: CANCELED <từ chối gửi thanh toán> 4: SENT ERROR <gửi thanh toán lỗi> 5: SENDED <đã gửi thanh toán> 6: RECEIVED <đã nhận kết quả>
	public int $status_payment;
	
	// Nội dung, ghi chú
	public ?string $description;
	
	// Dữ liệu liên quan lưu dạng json ojb
	public ?string $other_data;
	
	// Phiên bản nhận biết, Mặc định: 1.0
	public int $version;
	
	// 1: Trích tự động | 2: Trích tay
	public int $create_from;
	
	// Người tạo
	public ?string $created_by;
	
	// Người cập nhập cuối
	public ?string $updated_by;
	
	// Người duyệt
	public ?string $approved_by;
	
	// Người hủy, từ chối
	public ?string $canceled_by;
	
	// Người từ chói thanh toán
	public ?string $canceled_payment_by;
	
	// Người hoàn thành yêu cầu
	public ?string $completed_by;
	
	// Người thực hiện
	public ?string $completed_recheck_by;
	
	// Người ghi sổ
	public ?string $recored_by;
	
	// Người gửi
	public ?string $sended_by;
	
	// Người nhập kết quả từ đối tác
	public ?string $receivered_by;
	
	// Người thực hiện kiểm tra
	public ?string $checked_by;
	
	// Thời gian tạo
	public int $time_created;
	
	// Thời gian cập nhập cuối
	public ?int $time_updated;
	
	// Thời gian duyệt
	public ?int $time_approved;
	
	// Thời gian hủy, từ chối
	public ?int $time_canceled;
	
	// Thời gian từ chối thanh toán
	public ?int $time_canceled_payment;
	
	// Thời gian hoàn thành yêu cầu
	public ?int $time_completed;
	
	// Thời gian cập nhập hoàn thành nhưng cần check lại
	public ?int $time_completed_recheck;
	
	// Thời gian ghi sổ
	public ?int $time_recored;
	
	// Thời gian gửi lệnh sang đối tác
	public ?int $time_sended;
	
	// Thời gian nhận được kết quả đối tác báo hoặc qua check
	public ?int $time_receivered;
	
	// Thời gian kiểm tra gần nhất
	public ?int $time_checked;
	
	// id của yc thu hộ
	public ?int $request_payment_guide_id;

	public ?string $partner_code;

	public function __construct(
		int $type,
		int $profile_id,
		string $contract_code,
		string $payment_method_code,
		string $payment_channel_code,
		string $payment_account_id,
		?string $payment_account_holder_name = null,
		?string $payment_account_bank_code = null,
		?string $payment_account_bank_branch = null,
		string $partner_request_id,
		?string $partner_transaction_id = null,
		string $currency = 'VND',
		float $amount_request = 0.0,
		float $amount_payment = 0.0,
		float $amount_receiver = 0.0,
		float $fee = 0.0,
		int $time_begin = 0,
		?int $time_expired = null,
		int $is_payment = 1,
		int $status = 1,
		int $status_payment = 1,
		?string $description = null,
		?string $other_data = null,
		int $version = 1,
		int $create_from = 1,
		?string $created_by = null,
		?string $updated_by = null,
		?string $approved_by = null,
		?string $canceled_by = null,
		?string $canceled_payment_by = null,
		?string $completed_by = null,
		?string $completed_recheck_by = null,
		?string $recored_by = null,
		?string $sended_by = null,
		?string $receivered_by = null,
		?string $checked_by = null,
		int $time_created = 0,
		?int $time_updated = null,
		?int $time_approved = null,
		?int $time_canceled = null,
		?int $time_canceled_payment = null,
		?int $time_completed = null,
		?int $time_completed_recheck = null,
		?int $time_recored = null,
		?int $time_sended = null,
		?int $time_receivered = null,
		?int $time_checked = null,
		?int $request_payment_guide_id = null,
		?string $partner_code = null
	) {
		$this->type = $type;
		$this->profile_id = $profile_id;
		$this->contract_code = $contract_code;
		$this->payment_method_code = $payment_method_code;
		$this->payment_channel_code = $payment_channel_code;
		$this->payment_account_id = $payment_account_id;
		$this->payment_account_holder_name = $payment_account_holder_name;
		$this->payment_account_bank_code = $payment_account_bank_code;
		$this->payment_account_bank_branch = $payment_account_bank_branch;
		$this->partner_request_id = $partner_request_id;
		$this->partner_transaction_id = $partner_transaction_id;
		$this->currency = $currency;
		$this->amount_request = $amount_request;
		$this->amount_payment = $amount_payment;
		$this->amount_receiver = $amount_receiver;
		$this->fee = $fee;
		$this->time_begin = $time_begin;
		$this->time_expired = $time_expired;
		$this->is_payment = $is_payment;
		$this->status = $status;
		$this->status_payment = $status_payment;
		$this->description = $description;
		$this->other_data = $other_data;
		$this->version = $version;
		$this->create_from = $create_from;
		$this->created_by = $created_by;
		$this->updated_by = $updated_by;
		$this->approved_by = $approved_by;
		$this->canceled_by = $canceled_by;
		$this->canceled_payment_by = $canceled_payment_by;
		$this->completed_by = $completed_by;
		$this->completed_recheck_by = $completed_recheck_by;
		$this->recored_by = $recored_by;
		$this->sended_by = $sended_by;
		$this->receivered_by = $receivered_by;
		$this->checked_by = $checked_by;
		$this->time_created = $time_created;
		$this->time_updated = $time_updated;
		$this->time_approved = $time_approved;
		$this->time_canceled = $time_canceled;
		$this->time_canceled_payment = $time_canceled_payment;
		$this->time_completed = $time_completed;
		$this->time_completed_recheck = $time_completed_recheck;
		$this->time_recored = $time_recored;
		$this->time_sended = $time_sended;
		$this->time_receivered = $time_receivered;
		$this->time_checked = $time_checked;
		$this->request_payment_guide_id = $request_payment_guide_id;
		$this->partner_code = $partner_code;
	}

	/**
	 * Constructor overload với array
	 */
	public static function fromArray(array $data): self
	{
		// Set default values
		$defaults = [
			'type' => 1,
			'currency' => 'VND',
			'amount_receiver' => 0.0,
			'fee' => 0.0,
			'is_payment' => 1,
			'status' => 1,
			'status_payment' => 1,
			'version' => 1,
			'create_from' => 1,
			'time_created' => now()->timestamp,
		];
		
		$data = array_merge($defaults, $data);
		
		// Validate required fields
		$requiredFields = ['profile_id', 'contract_code', 'payment_method_code', 'payment_channel_code', 'payment_account_id', 'partner_request_id'];
		foreach ($requiredFields as $field) {
			if (!isset($data[$field])) {
				throw new \InvalidArgumentException("Field '{$field}' is required");
			}
		}
		
		return new self(
			$data['type'],
			$data['profile_id'],
			$data['contract_code'],
			$data['payment_method_code'],
			$data['payment_channel_code'],
			$data['payment_account_id'],
			$data['payment_account_holder_name'] ?? null,
			$data['payment_account_bank_code'] ?? null,
			$data['payment_account_bank_branch'] ?? null,
			$data['partner_request_id'],
			$data['partner_transaction_id'] ?? null,
			$data['currency'],
			$data['amount_request'] ?? 0.0,
			$data['amount_payment'] ?? 0.0,
			$data['amount_receiver'],
			$data['fee'],
			$data['time_begin'] ?? 0,
			$data['time_expired'] ?? null,
			$data['is_payment'],
			$data['status'],
			$data['status_payment'],
			$data['description'] ?? null,
			$data['other_data'] ?? null,
			$data['version'],
			$data['create_from'],
			$data['created_by'] ?? null,
			$data['updated_by'] ?? null,
			$data['approved_by'] ?? null,
			$data['canceled_by'] ?? null,
			$data['canceled_payment_by'] ?? null,
			$data['completed_by'] ?? null,
			$data['completed_recheck_by'] ?? null,
			$data['recored_by'] ?? null,
			$data['sended_by'] ?? null,
			$data['receivered_by'] ?? null,
			$data['checked_by'] ?? null,
			$data['time_created'],
			$data['time_updated'] ?? null,
			$data['time_approved'] ?? null,
			$data['time_canceled'] ?? null,
			$data['time_canceled_payment'] ?? null,
			$data['time_completed'] ?? null,
			$data['time_completed_recheck'] ?? null,
			$data['time_recored'] ?? null,
			$data['time_sended'] ?? null,
			$data['time_receivered'] ?? null,
			$data['time_checked'] ?? null,
			$data['request_payment_guide_id'] ?? null
		);
	}

	/**
	 * Tạo DTO từ RequestPaymentGuide model
	 */
	public static function fromRequestPaymentGuide(RequestPaymentGuide $guide): self
	{
		return new self(
			$guide->type ?? 1,
			$guide->profile_id,
			$guide->contract_code,
			'MPOS',
			'MPOS',
			$guide->getMposMcId(),
			null, // payment_account_holder_name
			null, // payment_account_bank_code
			null, // payment_account_bank_branch
			$guide->partner_request_id,
			$guide->partner_transaction_id ?? '',
			'VND', // currency
			(float) $guide->amount, // amount_request
			(float) $guide->amount, // amount_payment
			0.0, // amount_receiver
			0.0, // fee
			now()->startOfDay()->timestamp, // time_begin
			now()->setTime(22, 0, 0)->timestamp, // time_expired
			1, // is_payment
			1, // status
			1, // status_payment
			null, // description
			null, // other_data
			1, // version
			1, // create_from
			Helper::getSystemUser(), // created_by
			null, // updated_by
			null, // approved_by
			null, // canceled_by
			null, // canceled_payment_by
			null, // completed_by
			null, // completed_recheck_by
			null, // recored_by
			null, // sended_by
			null, // receivered_by
			null, // checked_by
			now()->timestamp, // time_created
			null, // time_updated
			null, // time_approved
			null, // time_canceled
			null, // time_canceled_payment
			null, // time_completed
			null, // time_completed_recheck
			null, // time_recored
			null, // time_sended
			null, // time_receivered
			null, // time_checked
			$guide->id, // request_payment_guide_id
		);
	}

	/**
	 * Chuyển đổi DTO thành array
	 */
	public function toArray(): array
	{
		return get_object_vars($this);
	}

} // End class