<?php

namespace App\Http\Middleware;

use App\Modules\CollectDebt\Model\CollectDebtSetting;
use Closure;
use Exception;
use Illuminate\Http\Request;

class EnableSettingMiddleware
{

  public function handle(Request $request, Closure $next, string $cronJobSettingName='')
  {
    $isEnableSetting = CollectDebtSetting::isEnableSetting(trim($cronJobSettingName));
    
    throw_if(
      !$isEnableSetting, 
      new Exception(sprintf('Setting: %s chua duoc bat', $cronJobSettingName))
    );
    
    return $next($request);
  }
}
