[2025-08-01 10:50:35] local.ERROR: <PERSON><PERSON> thống chưa ghi nhận mã hợp đồng thu nợ này {"exception":"[object] (App\\Exceptions\\BusinessException(code: 0): <PERSON><PERSON> thống chưa ghi nhận mã hợp đồng thu nợ này at C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Modules\\CollectDebt\\Controllers\\RequestPaymentReceipt\\RequestPaymentReceiptController.php:26)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\RequestPaymentReceipt\\RequestPaymentReceiptController->CreateRequestPaymentReceipt(Object(App\\Modules\\CollectDebt\\Requests\\RequestPayment\\CreateRequestPaymentReceiptRequest))
#1 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('CreateRequestPa...', Array)
#2 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\RequestPaymentReceipt\\RequestPaymentReceiptController), 'CreateRequestPa...')
#3 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#4 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#5 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\app\\Http\\Middleware\\CheckTokenMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\
equest-debt-bidv\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}
"} 
