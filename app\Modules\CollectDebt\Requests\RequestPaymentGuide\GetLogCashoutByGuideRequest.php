<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Foundation\Http\FormRequest;

class GetLogCashoutByGuideRequest extends FormRequest
{
	public function authorize()
	{
		return true;
	}

	public function rules()
	{
		return [
			'data' => ['required', 'array'],
			'data.partner_code' => ['required', 'string'],
			'data.partner_request_id' => ['required', 'string'],
		];
	}

	public function messages()
	{
		return [];
	}

} // End class
