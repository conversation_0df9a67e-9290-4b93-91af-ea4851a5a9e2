<?php

namespace App\Modules\CollectDebt\Requests\RequestPaymentGuide;

use Illuminate\Support\Facades\Log;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Modules\CollectDebt\Rules\MatchingPaymentAccountIdRule;

class CreateRequestPaymentGuideMultipleRequest extends FormRequest
{
  public function authorize()
  {
    return true;
  }

  public function rules()
  {
    return [
      'data' => ['bail', 'required', 'array'],
      'data.*.partner_code' => ['bail', 'required', 'string', 'max:15', 'in:NEXTLEND,TNEX,BIDV,VIETCREDIT,VPBANK'],
      'data.*.contract_code' => ['bail', 'required', 'string', 'max:50'],
      'data.*.amount' => ['bail', 'required', 'numeric', 'integer', 'min:1', 'max:**********'],
      'data.*.partner_request_id' => ['nullable', 'required_unless:data.*.partner_code,BIDV', 'string', 'max:50', 'regex:/^[A-Z0-9]+$/'], // mã đối tác
      'data.*.partner_request_account' => ['nullable', 'required_if:data.*.partner_code,BIDV', 'string', 'max:255'], // Tài khoản account đi theo yêu cầu
      'data.*.created_by' => ['bail', 'required', 'string', 'max:255'],
      'data.*.type' => ['bail', 'required', 'in:1,2'],
      'data.*.profile_id' => ['bail', 'required', 'numeric', 'min:0'],
      'data.*.payment_guide' => ['bail', 'required', 'array'],
      'data.*.payment_guide.*.payment_method_code' => ['required', 'string', 'in:MPOS,VIRTUALACCOUNT,IB_OFF'],
      'data.*.payment_guide.*.payment_channel_code' => ['required', 'string', 'in:MPOS,VIRTUALACCOUNT,IB_OFF'],
      'data.*.payment_guide.*.payment_account_id' => ['required', 'string', new MatchingPaymentAccountIdRule()],
      'data.*.payment_guide.*.other_data' => ['present', 'array'],

      'data.*.expired_at' => ['bail', 'nullable', 'date_format:Y-m-d H:i:s', 'after:+8 hours'],
      'data.*.requested_at' => ['bail', 'required', 'date_format:Y-m-d H:i:s'],
      'data.*.requested_by' => ['bail', 'required', 'string', 'max:255'],

      'data.*.merchant' => ['required', 'array'],
      'data.*.merchant.merchant_id' => ['required', 'integer', 'min:1'],
      'data.*.merchant.merchant_name' => ['required', 'string', 'max:255'],
      'data.*.merchant.representation_name' => ['nullable', 'string', 'max:255'],
      'data.*.merchant.email' => ['nullable', 'string', 'max:255', 'email'],
      'data.*.merchant.mobile' => ['nullable', 'string', 'max:20'],
      'data.*.merchant.address' => ['nullable', 'string', 'max:300'],
      'data.*.merchant.passport' => ['nullable', 'string', 'max:12'],

      'data.*.is_approved' => ['present', 'boolean'],
      'data.*.is_required_notify' => ['present', 'boolean']
    ];
  }

  public function messages()
  {
    return [
      'data.required' => 'Dữ liệu không được để trống',
      'data.array' => 'Dữ liệu phải là một mảng',
      'data.*.partner_code.required' => 'Mã đối tác không được để trống',
      'data.*.partner_code.string' => 'Mã đối tác phải là chuỗi ký tự',
      'data.*.partner_code.max' => 'Mã đối tác không được vượt quá 15 ký tự',
      'data.*.partner_code.in' => 'Mã đối tác không hợp lệ',
      'data.*.contract_code.required' => 'Mã hợp đồng là bắt buộc',
      'data.*.contract_code.string' => 'Mã hợp đồng phải là kiểu chuỗi',
      'data.*.contract_code.max' => 'Mã hợp đồng không được vượt quá 50 ký tự',
      'data.*.amount.required' => 'Số tiền không được để trống',
      'data.*.amount.numeric' => 'Số tiền phải là số',
      'data.*.amount.integer' => 'Số tiền phải là số nguyên',
      'data.*.amount.min' => 'Số tiền phải lớn hơn hoặc bằng 1.000',
      'data.*.amount.max' => 'Số tiền không được vượt quá 1.000.000.000',
      'data.*.partner_request_account.required_if' => 'Tài khoản đối tác là bắt buộc',
      'data.*.partner_request_id.required_unless' => 'Mã yêu cầu đối tác không được để trống',
      'data.*.partner_request_id.string' => 'Mã yêu cầu đối tác phải là chuỗi ký tự',
      'data.*.partner_request_id.max' => 'Mã yêu cầu đối tác không được vượt quá 50 ký tự',
      'data.*.partner_request_id.regex' => 'Mã yêu cầu đối tác chỉ được chứa chữ hoa và số',
      'data.*.created_by.required' => 'Người tạo không được để trống',
      'data.*.created_by.string' => 'Người tạo phải là chuỗi ký tự',
      'data.*.created_by.max' => 'Người tạo không được vượt quá 255 ký tự',
      'data.*.type.required' => 'Loại không được để trống',
      'data.*.type.in' => 'Loại phải là 1 hoặc 2',
      'data.*.profile_id.numeric' => 'ID hồ sơ phải là số',
      'data.*.profile_id.min' => 'ID hồ sơ phải lớn hơn hoặc bằng 0',
      'data.*.payment_guide.required' => 'Hướng dẫn thanh toán không được để trống',
      'data.*.payment_guide.array' => 'Hướng dẫn thanh toán phải là một mảng',
      'data.*.payment_guide.*.payment_method_code.required' => 'Phương thức thanh toán không được để trống',
      'data.*.payment_guide.*.payment_method_code.string' => 'Phương thức thanh toán phải là chuỗi ký tự',
      'data.*.payment_guide.*.payment_channel_code.required' => 'Kênh thanh toán không được để trống',
      'data.*.payment_guide.*.payment_channel_code.string' => 'Kênh thanh toán phải là chuỗi ký tự',
      'data.*.payment_guide.*.payment_account_id.required' => 'ID tài khoản thanh toán không được để trống',
      'data.*.payment_guide.*.payment_account_id.string' => 'ID tài khoản thanh toán phải là chuỗi ký tự',
      'data.*.payment_guide.*.other_data.present' => 'Dữ liệu khác phải có mặt',
      'data.*.payment_guide.*.other_data.array' => 'Dữ liệu khác phải là một mảng',
      'data.*.expired_at.required' => 'YC thu hộ phải có thời gian hết hạn',
      'data.*.expired_at.date_format' => 'YC thu hộ phải đúng định dạng YYYY-MM-DD HH:MM:SS',
      'data.*.expired_at.after' => 'Thời gian hết hạn phải sau thời gian hiện tại ít nhất 8 giờ',
      'data.*.merchant.other_data.array' => 'Dữ liệu khác của merchant phải là một mảng',
      'data.*.is_approved.present' => 'Trạng thái duyệt là bắt buộc',
      'data.*.is_approved.boolean' => 'Trạng thái duyệt chỉ chấp nhận giá trị: true hoặc false',
    ];
  }

  /**
   * Configure the validator instance.
   */
  public function withValidator($validator)
  {
    $validator->after(function ($validator) {
      $data = $this->input('data');

      if (!$data || !is_array($data)) {
        return;
      }

      // Kiểm tra contract_code phải khác nhau
      $contractCodes = [];
      $duplicates = [];

      foreach ($data as $index => $item) {
        $contractCode = $item['contract_code'] ?? '';

        if (empty($contractCode)) {
          continue;
        }

        if (in_array($contractCode, $contractCodes)) {
          $duplicates[] = $contractCode;
        } else {
          $contractCodes[] = $contractCode;
        }
      }

      // Nếu có contract_code trùng lặp
      if (!empty($duplicates)) {
        $uniqueDuplicates = array_unique($duplicates);
        $duplicateList = implode(', ', $uniqueDuplicates);
        $validator->errors()->add('data', "Các contract_code phải khác nhau. Các mã trùng lặp: {$duplicateList}");
      }
    });
  }

  protected function failedValidation(Validator $validator)
  {
    Log::error("CreateRequestPaymentGuideMultipleRequest", $this->all());

    app('alert')->sendMessage([
      'errors' => $validator->errors()->first()
    ], 'tao_yc_thu_ho');

    return parent::failedValidation($validator);
  }
} // End class
