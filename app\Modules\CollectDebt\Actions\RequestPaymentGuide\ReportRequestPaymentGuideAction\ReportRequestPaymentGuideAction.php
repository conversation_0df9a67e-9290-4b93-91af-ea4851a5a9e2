<?php

namespace App\Modules\CollectDebt\Actions\RequestPaymentGuide\ReportRequestPaymentGuideAction;

use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use App\Modules\CollectDebt\Requests\RequestPaymentGuide\ReportRequestPaymentGuideRequest;

class ReportRequestPaymentGuideAction
{
  public function run(ReportRequestPaymentGuideRequest $request)
  {
    $listContractCode = $request->json('data.list_contract_code', []);
    $partnerCode = $request->json('data.partner_code');

    $listRequestPaymentGuide = RequestPaymentGuide::query()
      ->where('partner_code', $partnerCode)
      ->whereIn('contract_code', $listContractCode)
      ->selectRaw("contract_code, SUM(amount_debited) as total_amount_success")
      ->groupBy('contract_code')
      ->get();

    return $listRequestPaymentGuide;
  }
} // End class
