<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\CancelRequestPaymentAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Model\RequestPaymentLog;
use App\Modules\CollectDebt\DTOs\RequestPayment\CancelRequestPaymentDTO;
use App\Modules\CollectDebtGateway\Repositories\CollectDebtGatewayRepository;

class CancelRequestPaymentAction
{
	public CollectDebtGatewayRepository $mposCollectDebtGatewayRepo;

	public function __construct(CollectDebtGatewayRepository $mposCollectDebtGatewayRepo)
	{
		$this->mposCollectDebtGatewayRepo = $mposCollectDebtGatewayRepo;
	}

	public function run(RequestPayment $requestPayment)
	{
		$dto = CancelRequestPaymentDTO::fromRequestPayment($requestPayment);
		$result = $this->mposCollectDebtGatewayRepo->cancelCollectDebtCommand($dto->toArray());

		RequestPaymentLog::query()->forceCreate([
			'contract_code' => $requestPayment->contract_code,
			'partner_request_id' => $requestPayment->partner_request_id,
			'func' => 'cancel',
			'request' => json_encode($dto->toArray()),
			'response' => json_encode($result),
		]);

		return $result;
	}

	public static function isCancelSuccess(array $result): bool {
		$cancelApiCode = $result['data']['error']['code'] ?? -1;

		if ($cancelApiCode == 1000) {
			return true;
		}

		return false;
	}
} // End class