<?php

namespace App\Traits;

use App\Utils\Result;

trait ApiResponser
{
  public function successResponse($data = [], string $message = 'Success', int $code = 200)
  {
    $message = $message ?? Result::$resultMessage[$code] ?? '';
    $response = [
      'success' => true,
      'result_code' => $code,
      'message' => $message,
      'data' => $data
    ];

    return response()->json($response, 200, [], JSON_UNESCAPED_UNICODE);
  }

  public function errorResponse($code = Result::ERROR, $message = null, $errors = [])
  {
    $message = !empty($message) ? $message : 'Đã có lỗi xảy ra, bạn vui lòng đợi kỹ thuật hỗ trợ xử lý.';
    $response = [
      'success' => false,
      'result_code' => $code,
      'message' => $message,
    ];

    if (empty($errors)) {
      $errors['errors'] = [$message ?? [$message] ?? 'Error'];
    }

    $response['data'] = $errors;
    return response()->json($response, 200, [], JSON_UNESCAPED_UNICODE);
  }
} // End class
