<?php

namespace App\Modules\CollectDebt\Model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class RequestPaymentReceipt extends Model
{
	protected $table = 'request_payment_receipt';
	public $timestamps = false;
	protected $appends = [];
	protected $guarded = [];

	public function requestPaymentGuide(): BelongsTo
	{
		return $this->belongsTo(RequestPaymentGuide::class, 'request_payment_guide_id', 'id');
	}

	public function requestPaymentGuideMerchant(): BelongsTo
	{
		return $this->belongsTo(RequestPaymentGuideMerchant::class, 'request_payment_guide_id', 'request_payment_guide_id');
	}

	public function requestPayment(): BelongsTo
	{
		return $this->belongsTo(RequestPayment::class, 'partner_request_id', 'partner_request_id');
	}
} // End class
