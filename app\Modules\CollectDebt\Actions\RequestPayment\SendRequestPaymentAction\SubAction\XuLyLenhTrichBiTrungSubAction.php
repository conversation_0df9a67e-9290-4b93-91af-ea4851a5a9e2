<?php

namespace App\Modules\CollectDebt\Actions\RequestPayment\SendRequestPaymentAction\SubAction;

use App\Modules\CollectDebt\Model\RequestPayment;
use App\Modules\CollectDebt\Actions\RequestPayment\CheckRequestPaymentAction\CheckRequestPaymentAction;

class XuLyLenhTrichBiTrungSubAction
{
	public CheckRequestPaymentAction $checkRequestPaymentAction;

	public function __construct(CheckRequestPaymentAction $checkRequestPaymentAction)
	{
		$this->checkRequestPaymentAction = $checkRequestPaymentAction;
	}

	public function run(RequestPayment $requestPayment)
	{
		$checkResult = $this->checkRequestPaymentAction->executeCheck($requestPayment);

		$this->checkRequestPaymentAction->handleCheckResult($requestPayment, $checkResult);

		return $requestPayment;
	}
} // End class