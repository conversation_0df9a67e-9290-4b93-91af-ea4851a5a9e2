<?php

namespace App\Modules\CollectDebt\Resources;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RequestPaymentGuideResourceCollection extends ResourceCollection
{
	public function toArray($request)
	{
		return [
			'data' => $this->collection->map(function (RequestPaymentGuide $requestPaymentGuide) {
				$can = ['CAN_VIEW_DEBT_HISTORY'];

				if ($requestPaymentGuide->status == CollectDebtEnum::REQUEST_PAYMENT_GUIDE_NEW) {
					$can[] = 'CAN_CANCEL_REQUEST_PAYMENT_GUIDE';
					$can[] = 'CAN_APPROVE_REQUEST_PAYMENT_GUIDE';
				}


				if ($requestPaymentGuide->status_cashout >= CollectDebtEnum::RPG_STT_CASHOUT_CREATED) {
					$can[] = 'CAN_VIEW_CASHOUT';
				}

				$requestPaymentGuide->can = $can;
				return $requestPaymentGuide;
			})->toArray(),
			'meta' => [
				'current_page' => $this->resource->currentPage(),
				'from' => $this->resource->firstItem(),
				'last_page' => $this->resource->lastPage(),
				'per_page' => $this->resource->perPage(),
				'to' => $this->resource->lastItem(),
				'total' => $this->resource->total(),
			],
			'links' => $this->resource->links(),
		];
	}
}
