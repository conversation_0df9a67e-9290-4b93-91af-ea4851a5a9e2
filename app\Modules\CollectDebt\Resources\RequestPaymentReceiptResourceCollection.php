<?php

namespace App\Modules\CollectDebt\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class RequestPaymentReceiptResourceCollection extends ResourceCollection
{
	public function toArray($request)
	{
		return [
			'data' => $this->collection->toArray(),
			'meta' => [
				'current_page' => $this->resource->currentPage(),
				'from' => $this->resource->firstItem(),
				'last_page' => $this->resource->lastPage(),
				'per_page' => $this->resource->perPage(),
				'to' => $this->resource->lastItem(),
				'total' => $this->resource->total(),
			],
			'links' => $this->resource->links(),
		];
	}
}
