<?php

namespace App\Modules\CollectDebt\Controllers\RequestPaymentGuide;

use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Exceptions\BusinessException;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Actions\RequestPaymentGuide\FindRequestPaymentGuideAction\FindRequestPaymentGuideAction;


class ContractController extends Controller
{
	public FindRequestPaymentGuideAction $action;

	public function __construct(FindRequestPaymentGuideAction $action)
	{
		$this->action = $action;
	}

  public function GetContractDetail(Request $request)
  {
		if (empty($request->json('data.partner_code'))) {
			throw new BusinessException('PartnerCode là bắt buộc');
		}

		if (empty($request->json('data.contract_code'))) {
			throw new BusinessException('ContractCode là bắt buộc');
		}

		$contractCode = trim($request->json('data.contract_code'));

		$contractData = $this->action->callGetContractDataCore($contractCode);
		
		if (empty($contractData['partner_code'])) {
			throw new BusinessException('Hợp đồng bị thiếu PartnerCode');
		}

		if ($contractData['partner_code'] != $request->json('data.partner_code')) {
			throw new BusinessException('Không khớp thông tin PartnerCode, bạn không có quyền truy cập vào hợp đồng này!!!');
		}

		$groupData = $this->action->getContractData($contractData);

    return $this->successResponse(array_merge($contractData, [
			'profiles' => $groupData,
		]));
  }
} // End class