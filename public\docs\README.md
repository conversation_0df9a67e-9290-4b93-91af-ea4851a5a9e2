# Tài liệu API - <PERSON><PERSON> thống Thu hộ nợ

## Tổng quan
Đây là tài liệu API cho hệ thống thu hộ nợ (Debt Collection System) của NextPay. Hệ thống này cung cấp các API để quản lý các chỉ dẫn thanh toán, lệnh trích nợ và tiền về.

## Danh sách API

### 1. Quản lý Chỉ dẫn Thanh toán (Request Payment Guide)

#### [GetAllRequestPaymentGuide](./get-all-request-payment-guide-api.md)
- **Mô tả**: <PERSON><PERSON><PERSON> danh sách chỉ dẫn thanh toán với phân trang và bộ lọc
- **Method**: GET
- **URL**: `/GetAllRequestPaymentGuide`

#### CreateRequestPaymentGuideBulk
- **Mô tả**: Tạo nhiều chỉ dẫn thanh toán cùng lúc
- **Method**: POST
- **URL**: `/CreateRequestPaymentGuideBulk`

#### ApproveRequestPaymentGuide
- **Mô tả**: Duyệt chỉ dẫn thanh toán
- **Method**: POST
- **URL**: `/ApproveRequestPaymentGuide`

#### CloseRequestPaymentGuide
- **Mô tả**: Đóng chỉ dẫn thanh toán
- **Method**: POST
- **URL**: `/CloseRequestPaymentGuide`

#### [FindRequestPaymentGuide](./find-request-payment-guide-api.md)
- **Mô tả**: Tra cứu chi tiết chỉ dẫn thanh toán theo ID
- **Method**: GET
- **URL**: `/FindRequestPaymentGuide`

### 2. Quản lý Lệnh Trích (Request Payment)

#### GetListRequestPayment
- **Mô tả**: Lấy danh sách lệnh trích với phân trang
- **Method**: GET
- **URL**: `/GetListRequestPayment`

#### GetDetailRequestPayment
- **Mô tả**: Lấy chi tiết lệnh trích
- **Method**: GET
- **URL**: `/GetDetailRequestPayment`

#### SendRequestPayment
- **Mô tả**: Gửi lệnh trích sang MPOS
- **Method**: POST
- **URL**: `/SendRequestPayment`

#### CheckRequestPayment
- **Mô tả**: Kiểm tra trạng thái lệnh trích
- **Method**: POST
- **URL**: `/CheckRequestPayment`

#### CheckPaymentRequestManual
- **Mô tả**: Kiểm tra lệnh trích thủ công
- **Method**: GET
- **URL**: `/CheckPaymentRequestManual`

#### NotifyRequestPayment
- **Mô tả**: Nhận kết quả trích nợ từ MPOS
- **Method**: POST
- **URL**: `/NotifyRequestPayment`

#### CutOffRequestPayment
- **Mô tả**: Cutoff lệnh trích về trạng thái cuối cùng
- **Method**: POST
- **URL**: `/CutOffRequestPayment`

#### HandleNotiResultRequestPayment
- **Mô tả**: Xử lý kết quả thông báo lệnh trích
- **Method**: POST
- **URL**: `/HandleNotiResultRequestPayment`

### 3. Quản lý Tiền Về (Request Payment Receipt)

#### CreateRequestPaymentReceipt
- **Mô tả**: Tạo bản ghi tiền về cho các kênh MPOS, VA, IB_OFF
- **Method**: POST
- **URL**: `/CreateRequestPaymentReceipt`

#### GetAllRequestPaymentReceipt
- **Mô tả**: Lấy tất cả lịch sử tiền về
- **Method**: GET
- **URL**: `/GetAllRequestPaymentReceipt`

#### MakeRequestPaymentFromReceipt
- **Mô tả**: Tạo lệnh trích từ nguồn tiền về
- **Method**: POST
- **URL**: `/MakeRequestPaymentFromReceipt`

### 4. Cấu hình

#### GetConfig
- **Mô tả**: Lấy cấu hình hệ thống
- **Method**: GET
- **URL**: `/GetConfig`

## Cấu trúc chung

### Authentication
Tất cả API đều yêu cầu xác thực thông qua Bearer token trong header:
```
Authorization: Bearer {token}
```

### Response Format
Tất cả API đều trả về response theo format chuẩn:
```json
{
  "success": true/false,
  "result_code": 200/400/500,
  "message": "Success/Error message",
  "data": {
    // Dữ liệu response
  }
}
```

### Error Handling
- **400**: Bad Request - Dữ liệu đầu vào không hợp lệ
- **401**: Unauthorized - Chưa xác thực hoặc token không hợp lệ
- **404**: Not Found - Tài nguyên không tồn tại
- **500**: Internal Server Error - Lỗi hệ thống

## Trạng thái hệ thống

### Trạng thái Chỉ dẫn Thanh toán
- `1`: Chờ duyệt
- `2`: Đã duyệt
- `3`: Đang xử lý
- `4`: Đã tạo lệnh trích
- `5`: Đã từ chối

### Trạng thái Lệnh Trích
- `1`: Chờ xử lý
- `2`: Đang xử lý
- `3`: Thành công
- `4`: Thất bại
- `5`: Đã hủy

### Loại Chỉ dẫn
- `1`: Thấu chi
- `2`: Khoản ứng

## Lưu ý quan trọng

1. **Timestamp**: Tất cả thời gian được trả về dưới dạng Unix timestamp
2. **Phân trang**: Các API danh sách đều hỗ trợ phân trang
3. **Validation**: Tất cả dữ liệu đầu vào đều được validate nghiêm ngặt
4. **Rate Limiting**: API có giới hạn số lượng request để đảm bảo hiệu suất hệ thống

## Liên hệ hỗ trợ

Nếu có thắc mắc hoặc cần hỗ trợ kỹ thuật, vui lòng liên hệ:
- Email: <EMAIL>
- Hotline: 1900-xxxx 