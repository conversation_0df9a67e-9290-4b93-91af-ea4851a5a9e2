<?php

namespace App\Modules\CollectDebt\DTOs\RequestPayment;

use App\Lib\Helper;
use App\Modules\CollectDebt\Model\RequestPaymentGuide;

class CreateRequestPaymentDTO
{
	public $type;
	public $profile_id;
	public $contract_code;
	public $payment_method_code;
	public $payment_channel_code;
	public $payment_account_id;
	public $amount_request;
	public $amount_payment;
	public $time_begin;
	public $time_expired;
	public $created_by;
	public $time_created;
	public $partner_request_id;
	public $partner_transaction_id;
	public $request_payment_guide_id;
	public $partner_code;

	public function __construct(
		int $type,
		int $profile_id,
		string $contract_code,
		string $payment_method_code,
		string $payment_channel_code,
		string $payment_account_id,
		string $partner_request_id,
		float $amount_request,
		float $amount_payment,
		int $time_begin,
		int $time_expired,
		string $created_by,
		int $time_created,
		string $partner_transaction_id,
		int $request_payment_guide_id,
		string $partner_code
	) {
		$this->type = $type;
		$this->profile_id = $profile_id;
		$this->contract_code = $contract_code;
		$this->payment_method_code = $payment_method_code;
		$this->payment_channel_code = $payment_channel_code;
		$this->payment_account_id = $payment_account_id;
		$this->partner_request_id = $partner_request_id;
		$this->amount_request = $amount_request;
		$this->amount_payment = $amount_payment;
		$this->time_begin = $time_begin;
		$this->time_expired = $time_expired;
		$this->created_by = $created_by;
		$this->time_created = $time_created;
		$this->partner_transaction_id = $partner_transaction_id;
		$this->request_payment_guide_id = $request_payment_guide_id;
		$this->partner_code = $partner_code;
	}

	/**
	 * Tạo DTO từ RequestPaymentGuide model
	 */
	public static function fromRequestPaymentGuide(RequestPaymentGuide $guide): self
	{
		return new self(
			$guide->type ?? 1,
			$guide->profile_id,
			$guide->contract_code,
			'MPOS',
			'MPOS',
			$guide->getMposMcId(),
			$guide->partner_request_id,
			(float) $guide->getSweepAmount(),
			(float) $guide->getSweepAmount(),
			now()->timestamp,
			$guide->time_expired,
			Helper::getSystemUser(),
			now()->timestamp,
			$guide->partner_transaction_id ?? '',
			$guide->id,
			$guide->partner_code
		);
	}

	public static function fromRequestPaymentGuideVA(RequestPaymentGuide $guide, string $paymentAccountId): self
	{
		return new self(
			$guide->type,
			$guide->profile_id,
			$guide->contract_code,
			'VIRTUALACCOUNT',
			'DYNAMIC_VA',
			$paymentAccountId,
			'', // partner_request_id sẽ update lại sau 
			(float) $guide->getSweepAmount(),
			(float) $guide->getSweepAmount(),
			now()->timestamp,
			$guide->time_expired,
			Helper::getSystemUser(),
			now()->timestamp,
			$guide->partner_transaction_id ?? '',
			$guide->id,
			$guide->partner_code
		);
	}

	/**
	 * Chuyển đổi DTO thành array
	 */
	public function toArray(): array
	{
		return get_object_vars($this);
	}

} // End class
